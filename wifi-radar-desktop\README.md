# WiFi Radar Desktop 🖥️📡

**Aplicativo Desktop Windows para Visualização de Redes WiFi Reais**

Um aplicativo nativo para Windows que escaneia e visualiza redes WiFi reais em uma interface de radar clássica, sem simulações - apenas dados reais do seu sistema.

## 🌟 Características Principais

- **✅ Dados Reais**: Escaneia redes WiFi reais do sistema Windows
- **🖥️ Aplicativo Nativo**: Executável .exe independente
- **📡 Interface Radar**: Visualização estilo radar militar
- **🔄 Auto-Scan**: Atualização automática a cada 5 segundos
- **📊 Informações Completas**: SSID, BSSID, RSSI, Canal, Segurança, Distância
- **🎯 Interativo**: Clique nos pontos para ver detalhes completos

## 🚀 Como Executar

### Modo Desenvolvimento
```bash
# Instalar dependências
npm install

# Executar aplicativo
npm start
# ou
npx electron .
```

### Gerar Executável (.exe)
```bash
# Instalar electron-builder (se não estiver instalado)
npm install electron-builder --save-dev

# Gerar executável para Windows
npm run build-win

# O arquivo .exe será criado em: dist/WiFi Radar Setup 1.0.0.exe
```

## 📋 Pré-requisitos

- **Windows 10/11**
- **Node.js** (versão 16 ou superior)
- **Placa WiFi ativa**
- **Permissões de administrador** (recomendado para melhor detecção)

## 🎯 Funcionalidades

### Interface Radar
- **Centro**: Representa seu PC
- **Pontos Coloridos**: Roteadores WiFi detectados
- **Distância**: Baseada na força do sinal (RSSI)
- **Cores**:
  - 🟢 **Verde**: Sinal forte (> -50 dBm)
  - 🟡 **Amarelo**: Sinal médio (-50 a -70 dBm)
  - 🟠 **Laranja**: Sinal fraco (-70 a -85 dBm)
  - 🔴 **Vermelho**: Sinal muito fraco (< -85 dBm)

### Painel de Redes
- **Lista Ordenada**: Por força do sinal
- **Barras de Sinal**: Indicador visual de 5 barras
- **Informações Rápidas**: RSSI, Canal, Frequência, Segurança
- **Contador**: Número total de redes detectadas

### Modal de Detalhes
Ao clicar em qualquer ponto do radar:
- **SSID**: Nome da rede
- **BSSID**: Endereço MAC do roteador
- **Força do Sinal**: Em dBm com barra visual
- **Canal**: Canal de transmissão
- **Frequência**: 2.4GHz ou 5GHz
- **Segurança**: WPA3, WPA2, WPA, WEP ou Aberto
- **Distância Estimada**: Calculada pela fórmula RSSI
- **Qualidade**: Percentual de qualidade
- **Última Detecção**: Timestamp da detecção

## 🔧 Tecnologias Utilizadas

- **Electron**: Framework para aplicativo desktop
- **Node.js**: Runtime JavaScript
- **node-wifi**: Biblioteca para escaneamento WiFi
- **HTML5/CSS3**: Interface moderna
- **JavaScript ES6+**: Lógica da aplicação

## 📊 Cálculo de Distância

Utiliza a fórmula padrão de propagação de RF:
```
distância (metros) ≈ 10^((TX Power - RSSI) / (10 * n))
```

Onde:
- **TX Power**: 20 dBm (padrão)
- **RSSI**: Força do sinal recebido
- **n**: 2.7 (fator de perda para ambientes internos)

## 🛠️ Estrutura do Projeto

```
wifi-radar-desktop/
├── main.js              # Processo principal do Electron
├── preload.js           # Script de pré-carregamento
├── package.json         # Configurações e dependências
├── renderer/            # Interface do usuário
│   ├── index.html       # Estrutura HTML
│   ├── styles.css       # Estilos CSS
│   └── script.js        # Lógica JavaScript
├── assets/              # Recursos (ícones, etc.)
└── dist/                # Executáveis gerados
```

## 🔍 Métodos de Escaneamento

O aplicativo utiliza múltiplos métodos para garantir detecção:

1. **node-wifi**: Biblioteca principal
2. **netsh (Windows)**: Comando nativo do Windows
3. **PowerShell**: Scripts para informações detalhadas

## ⚙️ Configurações Avançadas

### Alterar Intervalo de Auto-Scan
No arquivo `main.js`, linha ~200:
```javascript
autoScanInterval = setInterval(() => {
    // Altere 5000 para o intervalo desejado em ms
}, 5000);
```

### Ajustar Fórmula de Distância
No arquivo `main.js`, função `calculateDistance`:
```javascript
function calculateDistance(rssi, frequency = 2400, txPower = 20) {
    const n = 2.7; // Altere conforme o ambiente
    return Math.pow(10, (txPower - Math.abs(rssi)) / (10 * n));
}
```

## 🚨 Solução de Problemas

### "Nenhuma rede encontrada"
- Verifique se o WiFi está ativo
- Execute como administrador
- Verifique se há redes WiFi próximas

### "Erro no escaneamento"
- Reinstale as dependências: `npm install`
- Execute como administrador
- Verifique se o Windows permite acesso à rede

### Aplicativo não abre
- Verifique se o Node.js está instalado
- Execute: `npm install` novamente
- Tente: `npx electron .` diretamente

## 📦 Distribuição

### Gerar Instalador
```bash
npm run build-win
```

### Arquivos Gerados
- `WiFi Radar Setup 1.0.0.exe` - Instalador
- `WiFi Radar 1.0.0.exe` - Executável portátil

### Requisitos do Sistema
- **Windows 10/11** (64-bit)
- **4GB RAM** (mínimo)
- **50MB** espaço em disco
- **Placa WiFi** ativa

## 🔐 Permissões

O aplicativo pode solicitar:
- **Acesso à rede**: Para escanear WiFi
- **Permissões de administrador**: Para melhor detecção
- **Firewall**: Pode ser necessário permitir o acesso

## 📈 Performance

- **Consumo de RAM**: ~100-150MB
- **CPU**: Baixo uso (<5%)
- **Escaneamento**: ~1-3 segundos
- **Atualização**: A cada 5 segundos

## 🆕 Versões Futuras

- [ ] Exportar dados para CSV/JSON
- [ ] Histórico de redes detectadas
- [ ] Alertas de novas redes
- [ ] Mapa de calor de sinais
- [ ] Análise de interferência
- [ ] Modo escuro/claro

## 📞 Suporte

Para problemas ou sugestões:
1. Verifique os logs no console (F12)
2. Execute como administrador
3. Verifique a documentação do Windows WiFi
4. Reinstale as dependências

---

**Desenvolvido para Windows com dados reais de WiFi** 🖥️📡
