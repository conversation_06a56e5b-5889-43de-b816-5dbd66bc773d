class WiFiRadarDesktop {
    constructor() {
        this.networks = [];
        this.isScanning = false;
        this.radarRadius = 250; // Raio máximo do radar em pixels
        this.autoScanEnabled = true;
        this.autoScanInterval = null;
        this.centerX = this.radarRadius;
        this.centerY = this.radarRadius;

        this.initializeElements();
        this.setupEventListeners();
        this.setupElectronListeners();
        this.startInitialScan();
        this.startAutoScan();
    }

    initializeElements() {
        this.scanBtn = document.getElementById('scanBtn');
        this.status = document.getElementById('status');
        this.radarScreen = document.getElementById('radarScreen');
        this.networksList = document.getElementById('networksList');
        this.networkCount = document.getElementById('networkCount');
        this.lastUpdate = document.getElementById('lastUpdate');
        this.modal = document.getElementById('networkModal');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalBody = document.getElementById('modalBody');
        this.modalClose = document.getElementById('modalClose');
        this.modalCloseBtn = document.getElementById('modalCloseBtn');
        this.errorNotification = document.getElementById('errorNotification');
        this.errorMessage = document.getElementById('errorMessage');
        this.errorClose = document.getElementById('errorClose');
        this.autoScanBtn = document.getElementById('autoScanBtn');
    }

    setupEventListeners() {
        this.scanBtn.addEventListener('click', () => this.scanNetworks());
        this.autoScanBtn.addEventListener('click', () => this.toggleAutoScan());
        this.modalClose.addEventListener('click', () => this.closeModal());
        this.modalCloseBtn.addEventListener('click', () => this.closeModal());
        this.errorClose.addEventListener('click', () => this.hideError());

        window.addEventListener('click', (event) => {
            if (event.target === this.modal) this.closeModal();
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                if (this.modal.classList.contains('show')) this.closeModal();
                else if (this.errorNotification.classList.contains('show')) this.hideError();
            }
        });
    }

    setupElectronListeners() {
        window.electronAPI.onAutoScanTrigger(() => {
            if (this.autoScanEnabled && !this.isScanning) {
                this.scanNetworks(true);
            }
        });
    }

    startAutoScan() {
        this.autoScanBtn.classList.add('active');
        this.autoScanBtn.innerHTML = '<span class="btn-icon">⏸️</span>Pausar Auto-Scan';
        if (this.autoScanInterval) clearInterval(this.autoScanInterval);
        this.autoScanInterval = setInterval(() => {
            if (this.autoScanEnabled) this.scanNetworks(true);
        }, 5000);
    }

    toggleAutoScan() {
        this.autoScanEnabled = !this.autoScanEnabled;
        if (this.autoScanEnabled) {
            this.autoScanBtn.classList.add('active');
            this.autoScanBtn.innerHTML = '<span class="btn-icon">⏸️</span>Pausar Auto-Scan';
            this.startAutoScan();
            this.scanNetworks(true); // Scan immediately
        } else {
            this.autoScanBtn.classList.remove('active');
            this.autoScanBtn.innerHTML = '<span class="btn-icon">▶️</span>Iniciar Auto-Scan';
            if (this.autoScanInterval) clearInterval(this.autoScanInterval);
        }
    }

    async startInitialScan() {
        this.updateStatus('Iniciando escaneamento...', 'scanning');
        await this.scanNetworks(true);
    }

    updateStatus(message, className = '') {
        this.status.textContent = message;
        this.status.className = `status ${className}`;
    }

    async scanNetworks(isAutoScan = false) {
        if (this.isScanning) return;
        this.isScanning = true;

        if (!isAutoScan) {
            this.scanBtn.classList.add('scanning');
            this.scanBtn.innerHTML = '<span class="btn-icon">🔄</span>Escaneando...';
        }
        this.updateStatus('Escaneando redes WiFi...', 'scanning');

        try {
            const result = await window.electronAPI.scanWifi();
            if (result.success) {
                this.updateNetworks(result.networks);
                this.updateLastScanTime();
                if (result.networks.length === 0) {
                    this.updateStatus('Nenhuma rede encontrada', 'error');
                    this.showError('Nenhuma rede WiFi foi detectada. Verifique se o WiFi está ativo.');
                } else {
                    const methodInfo = result.scanMethod ? ` (${result.scanMethod})` : '';
                    const dataType = result.isRealData ? 'reais' : 'exemplo';
                    this.updateStatus(`${result.networks.length} redes ${dataType} detectadas${methodInfo}`, 'connected');
                    if (!result.isRealData) {
                        this.showError('Usando dados de exemplo. Execute como administrador para dados reais.');
                    }
                }
            } else {
                throw new Error(result.error || 'Erro desconhecido no escaneamento');
            }
        } catch (error) {
            console.error('Erro no escaneamento:', error);
            this.updateStatus('Erro no escaneamento', 'error');
            this.showError(`Erro ao escanear redes WiFi: ${error.message}`);
            this.updateNetworks([]);
        } finally {
            setTimeout(() => {
                this.isScanning = false;
                if (!isAutoScan) {
                    this.scanBtn.classList.remove('scanning');
                    this.scanBtn.innerHTML = '<span class="btn-icon">🔄</span>Escanear Agora';
                }
            }, 1000);
        }
    }

    updateNetworks(networks) {
        this.networks = networks;
        this.renderRadar();
        this.renderNetworksList();
        this.updateNetworkCount(networks.length);
    }

    updateNetworkCount(count) {
        this.networkCount.textContent = count === 1 ? '1 rede' : `${count} redes`;
    }

    updateLastScanTime() {
        this.lastUpdate.textContent = new Date().toLocaleTimeString('pt-BR');
    }

    renderRadar() {
        this.radarScreen.innerHTML = ''; // Clear previous points
        this.networks.forEach((network, index) => {
            const point = this.createRouterPoint(network, index);
            this.radarScreen.appendChild(point);
        });
    }

    createRouterPoint(network, index) {
        const point = document.createElement('div');
        point.className = `router-point ${this.getSignalClass(network.rssi)}`;
        const angle = (index * (360 / this.networks.length)) * (Math.PI / 180);
        const distance = this.calculateRadarDistance(network.rssi) * this.radarRadius;
        const x = this.centerX + Math.cos(angle) * distance;
        const y = this.centerY + Math.sin(angle) * distance;
        point.style.left = `${x - 6}px`;
        point.style.top = `${y - 6}px`;

        const label = document.createElement('div');
        label.className = 'router-label';
        label.textContent = network.ssid || 'Rede Oculta';
        point.appendChild(label);

        point.addEventListener('click', (e) => {
            e.stopPropagation();
            this.showNetworkDetails(network);
        });
        return point;
    }

    calculateRadarDistance(rssi) {
        const minRSSI = -100, maxRSSI = -20;
        const clampedRSSI = Math.max(minRSSI, Math.min(maxRSSI, rssi));
        const normalized = (maxRSSI - clampedRSSI) / (maxRSSI - minRSSI);
        const curved = Math.pow(normalized, 0.6);
        return 0.15 + (curved * 0.7);
    }

    getSignalClass(rssi) {
        if (rssi > -50) return 'strong';
        if (rssi > -70) return 'medium';
        if (rssi > -85) return 'weak';
        return 'very-weak';
    }

    getSignalStrength(rssi) {
        if (rssi > -50) return { text: 'Excelente', percentage: 100 };
        if (rssi > -60) return { text: 'Muito Bom', percentage: 80 };
        if (rssi > -70) return { text: 'Bom', percentage: 60 };
        if (rssi > -80) return { text: 'Fraco', percentage: 40 };
        return { text: 'Muito Fraco', percentage: 20 };
    }

    getSignalBars(rssi) {
        return Math.max(1, Math.min(5, Math.ceil(this.getSignalStrength(rssi).percentage / 20)));
    }

    renderNetworksList() {
        if (this.networks.length === 0) {
            this.networksList.innerHTML = `<div class="no-networks"><div class="loading-spinner"></div><p>Nenhuma rede WiFi detectada</p><small>Clique em "Escanear Agora" para tentar novamente</small></div>`;
            return;
        }
        const sortedNetworks = [...this.networks].sort((a, b) => b.rssi - a.rssi);
        this.networksList.innerHTML = sortedNetworks.map(network => {
            const signalBars = Array.from({length: 5}, (_, i) => `<div class="signal-bar ${i < this.getSignalBars(network.rssi) ? 'active' : ''}"></div>`).join('');
            return `
                <div class="network-item" onclick='wifiRadarDesktop.showNetworkDetails(${JSON.stringify(network).replace(/\"/g, "&quot;")})'>
                    <div class="network-signal">${signalBars}</div>
                    <div class="network-name">${network.ssid || 'Rede Oculta'}</div>
                    <div class="network-details">
                        <div class="network-detail-item"><span>Sinal:</span><span>${network.rssi} dBm</span></div>
                        <div class="network-detail-item"><span>Canal:</span><span>${network.channel}</span></div>
                        <div class="network-detail-item"><span>Frequência:</span><span>${network.frequencyBand}</span></div>
                        <div class="network-detail-item"><span>Segurança:</span><span>${network.security}</span></div>
                    </div>
                </div>`;
        }).join('');
    }

    showNetworkDetails(network) {
        const signalStrength = this.getSignalStrength(network.rssi);
        this.modalTitle.textContent = network.ssid || 'Rede Oculta';
        const bssidClean = network.bssid.replace(/:/g, '');

        this.modalBody.innerHTML = `
            <div class="info-section">
                <h4 class="section-title">📡 Informações Básicas</h4>
                <div class="detail-row"><span class="detail-label">SSID:</span><span class="detail-value">${network.ssid || 'Oculta'}</span></div>
                <div class="detail-row"><span class="detail-label">BSSID:</span><span class="detail-value">${network.bssid}</span></div>
                <div class="detail-row"><span class="detail-label">Segurança:</span><span class="detail-value security-badge ${network.security.toLowerCase()}">${network.security}</span></div>
            </div>
            <div class="info-section">
                <h4 class="section-title">📶 Informações do Sinal</h4>
                <div class="detail-row"><span class="detail-label">RSSI:</span><div class="detail-value signal-strength"><span>${network.rssi} dBm (${signalStrength.text})</span><div class="signal-bar-container"><div class="signal-fill" style="width: ${signalStrength.percentage}%; background: ${this.getSignalColor(network.rssi)};"></div></div></div></div>
                <div class="detail-row"><span class="detail-label">Qualidade:</span><span class="detail-value">${network.quality || 'N/A'}%</span></div>
                <div class="detail-row"><span class="detail-label">Distância:</span><span class="detail-value">${network.distance}m</span></div>
            </div>
            <div class="info-section">
                <h4 class="section-title">💻 Terminal Interativo</h4>
                <div class="terminal-container">
                    <div class="terminal-output" id="terminal-output-${bssidClean}"></div>
                    <div class="terminal-input-container">
                        <span class="terminal-prompt">></span>
                        <input type="text" class="terminal-input" id="terminal-input-${bssidClean}" placeholder="Digite 'help' para ver os comandos...">
                    </div>
                </div>
            </div>`;

        const terminalInput = document.getElementById(`terminal-input-${bssidClean}`);
        terminalInput.addEventListener('keypress', (e) => this.handleTerminalInput(e, network.bssid, network));

        this.modal.classList.add('show');
        setTimeout(() => terminalInput.focus(), 100);
    }

    getSignalColor(rssi) {
        if (rssi > -50) return '#00ff41';
        if (rssi > -70) return '#ffff00';
        if (rssi > -85) return '#ff6600';
        return '#ff0000';
    }

    closeModal() {
        this.modal.classList.remove('show');
    }

    showError(message) {
        this.errorMessage.textContent = message;
        this.errorNotification.classList.add('show');
        setTimeout(() => this.hideError(), 8000);
    }

    hideError() {
        this.errorNotification.classList.remove('show');
    }

    addTerminalLine(terminal, text, className) {
        const line = document.createElement('div');
        line.className = `terminal-line ${className || ''}`;
        const pre = document.createElement('pre');
        pre.textContent = text;
        line.appendChild(pre);
        terminal.appendChild(line);
        terminal.scrollTop = terminal.scrollHeight;
    }

    handleTerminalInput(event, bssid, network) {
        if (event.key === 'Enter') {
            const input = event.target;
            const command = input.value.trim();
            if (command) {
                this.executeTerminalCommand(command, bssid, network);
                input.value = '';
            }
        }
    }

    async executeTerminalCommand(command, bssid, network) {
        const terminal = document.getElementById(`terminal-output-${bssid.replace(/:/g, '')}`);
        const [cmd, ...args] = command.toLowerCase().split(' ');
        this.addTerminalLine(terminal, `> ${command}`, 'input');

        switch (cmd) {
            case 'help': this.showHelp(terminal); break;
            case 'ping': await this.executePing(terminal, args[0] || 4); break;
            case 'traceroute': await this.executeTraceroute(terminal); break;
            case 'nslookup': await this.executeNslookup(terminal, args[0] || 'google.com'); break;
            case 'connect': await this.connectToNetwork(terminal, network, args.join(' ')); break;
            case 'disconnect': await this.disconnectFromNetwork(terminal); break;
            case 'status': await this.getConnectionStatus(terminal); break;
            case 'config': await this.getNetworkConfig(terminal); break;
            case 'info': this.showDetailedInfo(terminal, network); break;
            case 'clear': case 'cls': this.clearTerminal(terminal); break;
            default: this.addTerminalLine(terminal, `Comando não reconhecido: ${command}`, 'error'); break;
        }
    }

    showHelp(terminal) {
        const helpText = [
            {t: '--- Comandos Disponíveis ---', c: 'info'},
            {t: 'ping [count]     - Testa a latência com o Google DNS.', c: 'output'},
            {t: 'traceroute       - Rastreia a rota até o Google DNS.', c: 'output'},
            {t: 'nslookup [host]  - Resolve um nome de domínio.', c: 'output'},
            {t: 'connect <senha>  - Conecta a esta rede (se necessário).', c: 'output'},
            {t: 'disconnect       - Desconecta da rede WiFi atual.', c: 'output'},
            {t: 'status           - Mostra o status da conexão WiFi.', c: 'output'},
            {t: 'config           - Exibe a configuração de rede (ipconfig).', c: 'output'},
            {t: 'info             - Mostra informações detalhadas desta rede.', c: 'output'},
            {t: 'clear / cls      - Limpa o terminal.', c: 'output'},
        ];
        helpText.forEach(line => this.addTerminalLine(terminal, line.t, line.c));
    }

    async executePing(terminal, count) {
        this.addTerminalLine(terminal, `Executando ping para ******* (${count} vezes)...`, 'info');
        const result = await window.electronAPI.pingHost('*******', count);
        this.addTerminalLine(terminal, result.output, result.success ? 'success' : 'error');
    }

    async executeTraceroute(terminal) {
        this.addTerminalLine(terminal, 'Executando traceroute para *******...', 'info');
        const result = await window.electronAPI.tracerouteHost('*******');
        this.addTerminalLine(terminal, result.output, result.success ? 'output' : 'error');
    }

    async executeNslookup(terminal, host) {
        this.addTerminalLine(terminal, `Executando nslookup para ${host}...`, 'info');
        const result = await window.electronAPI.nslookupHost(host);
        this.addTerminalLine(terminal, result.output, result.success ? 'output' : 'error');
    }

    async connectToNetwork(terminal, network, password) {
        this.addTerminalLine(terminal, `Tentando conectar à rede ${network.ssid}...`, 'info');
        if (network.security !== 'Open' && !password) {
            this.addTerminalLine(terminal, 'Esta rede requer uma senha. Use: connect <senha>', 'error');
            return;
        }
        const result = await window.electronAPI.connectWifi(network.ssid, password);
        this.addTerminalLine(terminal, result.message, result.success ? 'success' : 'error');
    }

    async disconnectFromNetwork(terminal) {
        this.addTerminalLine(terminal, 'Desconectando da rede WiFi atual...', 'info');
        const result = await window.electronAPI.disconnectWifi();
        this.addTerminalLine(terminal, result.message, result.success ? 'success' : 'error');
    }

    async getConnectionStatus(terminal) {
        this.addTerminalLine(terminal, 'Verificando status da conexão...', 'info');
        const result = await window.electronAPI.getWifiStatus();
        this.addTerminalLine(terminal, result.output, result.success ? 'output' : 'error');
    }

    async getNetworkConfig(terminal) {
        this.addTerminalLine(terminal, 'Executando ipconfig...', 'info');
        const result = await window.electronAPI.getNetworkInfo();
        this.addTerminalLine(terminal, result.output, result.success ? 'output' : 'error');
    }

    clearTerminal(terminal) {
        terminal.innerHTML = '';
    }
}

document.addEventListener('DOMContentLoaded', () => {
    try {
        window.wifiRadarDesktop = new WiFiRadarDesktop();
    } catch (error) {
        console.error('Falha ao inicializar a aplicação:', error);
        const status = document.getElementById('status');
        if (status) {
            status.textContent = 'Erro crítico na inicialização.';
            status.className = 'status error';
        }
    }
});
