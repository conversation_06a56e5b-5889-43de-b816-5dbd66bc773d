# WiFi Radar 📡

Um aplicativo web moderno que escaneia e visualiza redes WiFi em uma interface de radar clássica.

## 🌟 Funcionalidades

- **Interface Radar**: Visualização estilo radar com o usuário no centro
- **Escaneamento WiFi**: Detecta automaticamente redes WiFi disponíveis
- **Informações Detalhadas**: SSID, BSSID, RSSI, Canal, Segurança, Frequência
- **Estimativa de Distância**: Calcula distância aproximada baseada no RSSI
- **Atualização Automática**: Refresh automático a cada 5 segundos
- **Interface Responsiva**: Compatível com desktop e mobile
- **Tempo Real**: WebSocket para atualizações em tempo real

## 🚀 Como Usar

### Pré-requisitos

- Node.js (versão 14 ou superior)
- Windows (para acesso às informações de WiFi)
- Placa de rede WiFi ativa

### Instalação

1. **Instalar dependências:**
   ```bash
   npm install
   ```

2. **Executar o aplicativo:**
   ```bash
   npm start
   ```

3. **Abrir no navegador:**
   - Acesse: `http://localhost:3000`

### Desenvolvimento

Para desenvolvimento com auto-reload:
```bash
npm run dev
```

## 🎯 Como Funciona

### Interface Radar

- **Centro**: Representa sua localização
- **Pontos Coloridos**: Roteadores WiFi detectados
- **Distância do Centro**: Baseada na força do sinal (RSSI)
- **Cores dos Pontos**:
  - 🟢 Verde: Sinal forte (> -50 dBm)
  - 🟡 Amarelo: Sinal médio (-50 a -70 dBm)
  - 🟠 Laranja: Sinal fraco (-70 a -85 dBm)
  - 🔴 Vermelho: Sinal muito fraco (< -85 dBm)

### Cálculo de Distância

Utiliza a fórmula:
```
distância (metros) ≈ 10^((TX Power - RSSI) / (10 * n))
```

Onde:
- **TX Power**: Potência de transmissão (padrão: 20 dBm)
- **RSSI**: Força do sinal recebido
- **n**: Fator de perda (2.7 para ambientes internos)

### Interação

1. **Clique em "Escanear"**: Inicia busca manual por redes
2. **Clique nos pontos**: Abre modal com informações detalhadas
3. **Atualização automática**: Interface se atualiza a cada 5 segundos
4. **Lista lateral**: Mostra todas as redes ordenadas por força do sinal

## 🛠️ Tecnologias Utilizadas

### Backend
- **Node.js**: Servidor principal
- **Express**: Framework web
- **node-wifi**: Biblioteca para escaneamento WiFi
- **WebSocket**: Comunicação em tempo real
- **CORS**: Suporte a requisições cross-origin

### Frontend
- **HTML5**: Estrutura da página
- **CSS3**: Estilização moderna com gradientes e animações
- **JavaScript ES6+**: Lógica da aplicação
- **WebSocket API**: Comunicação em tempo real
- **Canvas/CSS**: Visualização do radar

## 📊 Informações Exibidas

Para cada rede WiFi detectada:

- **SSID**: Nome da rede
- **BSSID**: Endereço MAC do roteador
- **RSSI**: Força do sinal em dBm
- **Canal**: Canal de transmissão
- **Frequência**: 2.4GHz ou 5GHz
- **Segurança**: WPA3, WPA2, WPA, WEP ou Aberto
- **Distância**: Estimativa em metros
- **Qualidade**: Percentual de qualidade do sinal

## 🔧 Configuração Avançada

### Personalizar Intervalo de Atualização

Edite o arquivo `server.js`, linha com `setInterval`:
```javascript
const interval = setInterval(scanAndBroadcast, 5000); // 5000ms = 5 segundos
```

### Ajustar Fórmula de Distância

Modifique a função `calculateDistance` no `server.js`:
```javascript
function calculateDistance(rssi, frequency = 2400, txPower = 20) {
    const n = 2.7; // Altere este valor conforme o ambiente
    return Math.pow(10, (txPower - Math.abs(rssi)) / (10 * n));
}
```

## ⚠️ Limitações

- **Windows**: Funciona melhor no Windows devido às APIs de rede
- **Permissões**: Pode requerer execução como administrador
- **Precisão**: Estimativas de distância são aproximadas
- **Redes Ocultas**: Podem não ser detectadas completamente

## 🐛 Solução de Problemas

### Erro "Nenhuma rede encontrada"
- Verifique se o WiFi está ativo
- Execute como administrador
- Verifique se há redes WiFi próximas

### WebSocket não conecta
- Verifique se a porta 3000 está livre
- Desative firewall temporariamente
- Verifique se o servidor está rodando

### Interface não carrega
- Limpe o cache do navegador
- Verifique o console do navegador (F12)
- Certifique-se que todos os arquivos estão presentes

## 📝 Licença

MIT License - Veja o arquivo LICENSE para detalhes.

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor:

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📞 Suporte

Para dúvidas ou problemas:
- Abra uma issue no GitHub
- Verifique a documentação das dependências
- Consulte os logs do servidor no terminal

---

**Desenvolvido com ❤️ para visualização de redes WiFi**
