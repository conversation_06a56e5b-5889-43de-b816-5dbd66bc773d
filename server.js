const express = require('express');
const cors = require('cors');
const wifi = require('node-wifi');
const WebSocket = require('ws');
const path = require('path');

const app = express();
const PORT = 3001;

// Configurar middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Inicializar WiFi
wifi.init({
    iface: null // network interface, choose a random wifi interface if set to null
});

// Função para calcular distância estimada baseada no RSSI
function calculateDistance(rssi, frequency = 2400, txPower = 20) {
    // Ajustar parâmetros baseados na frequência
    let n, txPowerAdjusted;

    if (frequency >= 5000) {
        // 5GHz - maior atenuação, menor alcance
        n = 3.2; // Fator de perda maior para 5GHz
        txPowerAdjusted = txPower - 3; // 5GHz geralmente tem menor potência efetiva
    } else {
        // 2.4GHz - menor atenuação, maior alcance
        n = 2.4; // Fator de perda menor para 2.4GHz
        txPowerAdjusted = txPower;
    }

    // Fórmula melhorada: distância = 10^((TX Power - RSSI - 32.44 - 20*log10(freq)) / (10 * n))
    // Onde 32.44 é a perda de espaço livre a 1 metro para 2.4GHz
    const freqMHz = frequency;
    const freeSpaceLoss = 32.44 + 20 * Math.log10(freqMHz);

    // Calcular distância usando modelo de propagação mais realista
    const pathLoss = txPowerAdjusted - rssi;
    const distance = Math.pow(10, (pathLoss - freeSpaceLoss) / (10 * n));

    // Aplicar limites realistas
    let finalDistance = Math.max(0.1, distance); // Mínimo 10cm
    finalDistance = Math.min(finalDistance, 500); // Máximo 500m

    // Arredondar baseado na distância
    if (finalDistance < 1) {
        return Math.round(finalDistance * 100) / 100; // 2 casas decimais para < 1m
    } else if (finalDistance < 10) {
        return Math.round(finalDistance * 10) / 10; // 1 casa decimal para < 10m
    } else {
        return Math.round(finalDistance); // Sem casas decimais para >= 10m
    }
}

// Função para determinar o tipo de segurança
function getSecurityType(security) {
    if (!security) return 'Aberto';

    // Se security é uma string, converter para array
    let securityArray = Array.isArray(security) ? security : [security];

    if (securityArray.length === 0) return 'Aberto';

    const securityStr = securityArray.join(' ').toLowerCase();

    if (securityStr.includes('wpa3')) return 'WPA3';
    if (securityStr.includes('wpa2')) return 'WPA2';
    if (securityStr.includes('wpa')) return 'WPA';
    if (securityStr.includes('wep')) return 'WEP';

    return 'Outros';
}

// Função para determinar a banda de frequência
function getFrequencyBand(channel) {
    // Canais 1-14 são 2.4GHz, canais acima de 32 são 5GHz
    if (channel >= 1 && channel <= 14) {
        return '2.4GHz';
    } else if (channel >= 32) {
        return '5GHz';
    }
    return 'Desconhecido';
}

// Função para gerar direção baseada no BSSID (simulação realista)
function generateDirection(bssid) {
    const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
    // Usar o último octeto do BSSID para gerar direção consistente
    const lastOctet = parseInt(bssid.split(':').pop(), 16);
    return directions[lastOctet % directions.length];
}

// Função para gerar redes WiFi simuladas para demonstração
function generateSimulatedNetworks() {
    const simulatedNetworks = [
        {
            ssid: 'HOME_WIFI_2.4G',
            bssid: '00:1A:2B:3C:4D:5E',
            rssi: -45,
            channel: 6,
            frequency: 2437,
            security: ['WPA2'],
            quality: 85
        },
        {
            ssid: 'VIZINHO_NET',
            bssid: '00:2B:3C:4D:5E:6F',
            rssi: -65,
            channel: 11,
            frequency: 2462,
            security: ['WPA2', 'WPA3'],
            quality: 60
        },
        {
            ssid: 'APARTAMENTO_101',
            bssid: '00:3C:4D:5E:6F:7A',
            rssi: -72,
            channel: 1,
            frequency: 2412,
            security: ['WPA2'],
            quality: 45
        },
        {
            ssid: 'CAFE_LIVRE',
            bssid: '00:4D:5E:6F:7A:8B',
            rssi: -55,
            channel: 36,
            frequency: 5180,
            security: [],
            quality: 70
        },
        {
            ssid: 'EMPRESA_GUEST',
            bssid: '00:5E:6F:7A:8B:9C',
            rssi: -80,
            channel: 44,
            frequency: 5220,
            security: ['WPA2'],
            quality: 30
        },
        {
            ssid: 'ROTEADOR_5G',
            bssid: '00:6F:7A:8B:9C:AD',
            rssi: -50,
            channel: 149,
            frequency: 5745,
            security: ['WPA3'],
            quality: 80
        },
        {
            ssid: 'NET_CLARO_WIFI',
            bssid: '00:7A:8B:9C:AD:BE',
            rssi: -88,
            channel: 3,
            frequency: 2422,
            security: ['WPA2'],
            quality: 20
        },
        {
            ssid: 'LOJA_WIFI',
            bssid: '00:8B:9C:AD:BE:CF',
            rssi: -75,
            channel: 157,
            frequency: 5785,
            security: ['WPA2'],
            quality: 40
        },
        {
            ssid: '',
            bssid: '00:9C:AD:BE:CF:D0',
            rssi: -85,
            channel: 9,
            frequency: 2452,
            security: ['WEP'],
            quality: 25
        },
        {
            ssid: 'FIBRA_ULTRA',
            bssid: '00:AD:BE:CF:D0:E1',
            rssi: -60,
            channel: 40,
            frequency: 5200,
            security: ['WPA3'],
            quality: 65
        }
    ];

    return simulatedNetworks.map(network => ({
        ssid: network.ssid || 'Rede Oculta',
        bssid: network.bssid,
        rssi: network.rssi,
        channel: network.channel,
        frequency: network.frequency,
        security: getSecurityType(network.security),
        securityFlags: network.security || [],
        frequencyBand: getFrequencyBand(network.channel),
        distance: calculateDistance(network.rssi, network.frequency),
        quality: network.quality,
        direction: generateDirection(network.bssid)
    }));
}

// Endpoint para escanear redes WiFi
app.get('/api/scan', async (req, res) => {
    try {
        console.log('Iniciando escaneamento WiFi...');
        
        let networks = [];
        let useSimulated = false;
        
        try {
            networks = await wifi.scan();
            console.log(`Escaneamento real encontrou ${networks.length} redes`);
        } catch (scanError) {
            console.log('Erro no escaneamento real, usando dados simulados:', scanError.message);
            useSimulated = true;
        }
        
        // Se não encontrou redes reais ou houve erro, usar dados simulados
        if (networks.length === 0 || useSimulated) {
            console.log('Usando redes WiFi simuladas para demonstração');
            const simulatedNetworks = generateSimulatedNetworks();
            
            res.json({
                success: true,
                networks: simulatedNetworks,
                simulated: true,
                timestamp: new Date().toISOString()
            });
            return;
        }
        
        const processedNetworks = networks.map(network => ({
            ssid: network.ssid || 'Rede Oculta',
            bssid: network.bssid || 'N/A',
            rssi: network.signal_level || -70,
            channel: network.channel || 0,
            frequency: network.frequency || 2400,
            security: getSecurityType(network.security),
            securityFlags: network.security || [],
            frequencyBand: getFrequencyBand(network.channel),
            distance: calculateDistance(network.signal_level || -70, network.frequency || 2400),
            quality: network.quality || 0,
            direction: generateDirection(network.bssid || '00:00:00:00:00:00')
        }));

        console.log(`Encontradas ${processedNetworks.length} redes WiFi reais`);
        res.json({
            success: true,
            networks: processedNetworks,
            simulated: false,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error('Erro geral no escaneamento:', error);
        
        // Em caso de erro, retornar dados simulados
        console.log('Retornando dados simulados devido ao erro');
        const simulatedNetworks = generateSimulatedNetworks();
        
        res.json({
            success: true,
            networks: simulatedNetworks,
            simulated: true,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Servir arquivos estáticos
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Criar servidor WebSocket para atualizações em tempo real
const server = require('http').createServer(app);
const wss = new WebSocket.Server({ server });

wss.on('connection', (ws) => {
    console.log('Cliente WebSocket conectado');
    
    // Enviar dados iniciais
    scanAndBroadcast();
    
    // Configurar intervalo de atualização automática
    const interval = setInterval(scanAndBroadcast, 5000);
    
    ws.on('close', () => {
        console.log('Cliente WebSocket desconectado');
        clearInterval(interval);
    });
    
    ws.on('message', (message) => {
        const data = JSON.parse(message);
        if (data.type === 'scan') {
            scanAndBroadcast();
        }
    });
});

async function scanAndBroadcast() {
    try {
        let networks = [];
        let useSimulated = false;
        let processedNetworks = [];
        
        try {
            networks = await wifi.scan();
            console.log(`WebSocket: Escaneamento real encontrou ${networks.length} redes`);
        } catch (scanError) {
            console.log('WebSocket: Erro no escaneamento real, usando dados simulados:', scanError.message);
            useSimulated = true;
        }
        
        // Se não encontrou redes reais ou houve erro, usar dados simulados
        if (networks.length === 0 || useSimulated) {
            console.log('WebSocket: Usando redes WiFi simuladas');
            processedNetworks = generateSimulatedNetworks();
        } else {
            processedNetworks = networks.map(network => ({
                ssid: network.ssid || 'Rede Oculta',
                bssid: network.bssid || 'N/A',
                rssi: network.signal_level || -70,
                channel: network.channel || 0,
                frequency: network.frequency || 2400,
                security: getSecurityType(network.security),
                securityFlags: network.security || [],
                frequencyBand: getFrequencyBand(network.channel),
                distance: calculateDistance(network.signal_level || -70, network.frequency || 2400),
                quality: network.quality || 0,
                direction: generateDirection(network.bssid || '00:00:00:00:00:00')
            }));
        }

        const data = {
            type: 'networks',
            networks: processedNetworks,
            simulated: useSimulated || networks.length === 0,
            timestamp: new Date().toISOString()
        };

        // Broadcast para todos os clientes conectados
        wss.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(JSON.stringify(data));
            }
        });
        
    } catch (error) {
        console.error('Erro no escaneamento automático:', error);
        
        // Em caso de erro, enviar dados simulados
        const simulatedNetworks = generateSimulatedNetworks();
        const data = {
            type: 'networks',
            networks: simulatedNetworks,
            simulated: true,
            error: error.message,
            timestamp: new Date().toISOString()
        };

        wss.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(JSON.stringify(data));
            }
        });
    }
}

server.listen(PORT, () => {
    console.log(`Servidor WiFi Radar rodando em http://localhost:${PORT}`);
    console.log('Pressione Ctrl+C para parar o servidor');
});
