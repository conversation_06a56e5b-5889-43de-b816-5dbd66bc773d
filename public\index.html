<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WiFi Radar - Escaneador de Redes</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">
                <span class="radar-icon">📡</span>
                WiFi Radar
            </h1>
            <div class="controls">
                <button id="scanBtn" class="scan-btn">
                    <span class="btn-icon">🔄</span>
                    Escanear
                </button>
                <div class="status" id="status">
                    <span class="status-dot"></span>
                    Aguardando...
                </div>
            </div>
        </header>

        <main class="main-content">
            <div class="radar-container">
                <div class="radar-screen" id="radarScreen">
                    <!-- Círculos concêntricos do radar -->
                    <div class="radar-circle" style="width: 100px; height: 100px;"></div>
                    <div class="radar-circle" style="width: 200px; height: 200px;"></div>
                    <div class="radar-circle" style="width: 300px; height: 300px;"></div>
                    <div class="radar-circle" style="width: 400px; height: 400px;"></div>
                    
                    <!-- Linhas do radar -->
                    <div class="radar-line radar-line-h"></div>
                    <div class="radar-line radar-line-v"></div>
                    <div class="radar-line radar-line-d1"></div>
                    <div class="radar-line radar-line-d2"></div>
                    
                    <!-- Centro do radar (usuário) -->
                    <div class="radar-center">
                        <div class="user-dot"></div>
                        <span class="user-label">Você</span>
                    </div>
                    
                    <!-- Sweep do radar -->
                    <div class="radar-sweep" id="radarSweep"></div>
                    
                    <!-- Pontos dos roteadores serão inseridos aqui -->
                </div>
                
                <div class="radar-legend">
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #00ff41;"></div>
                        <span>Sinal Forte (&gt; -50 dBm)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #ffff00;"></div>
                        <span>Sinal Médio (-50 a -70 dBm)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #ff6600;"></div>
                        <span>Sinal Fraco (-70 a -85 dBm)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #ff0000;"></div>
                        <span>Sinal Muito Fraco (&lt; -85 dBm)</span>
                    </div>
                </div>
            </div>

            <div class="info-panel">
                <h3>Redes Detectadas</h3>
                <div class="networks-list" id="networksList">
                    <div class="no-networks">
                        <p>Clique em "Escanear" para detectar redes WiFi</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal para detalhes da rede -->
    <div class="modal" id="networkModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Detalhes da Rede</h2>
                <button class="modal-close" id="modalClose">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Conteúdo será inserido dinamicamente -->
            </div>
            <div class="wifi-actions" id="wifiActions" style="display: none;">
                <div class="actions-header">
                    <span class="actions-title">🔧 Ações WiFi</span>
                    <div class="signal-indicator" id="signalIndicator">
                        <span class="signal-strength-text" id="signalStrengthText">Sinal: --</span>
                        <div class="signal-bars" id="signalBars">
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                        </div>
                    </div>
                </div>

                <div class="actions-grid">
                    <button class="action-btn primary" id="pingSignalBtn">
                        <span class="btn-icon">📡</span>
                        <span class="btn-text">Ping Sinal</span>
                        <span class="btn-desc">Testar latência direta</span>
                    </button>

                    <button class="action-btn success" id="connectSignalBtn">
                        <span class="btn-icon">🔗</span>
                        <span class="btn-text">Conectar</span>
                        <span class="btn-desc">Conectar à rede</span>
                    </button>

                    <button class="action-btn info" id="analyzeSignalBtn">
                        <span class="btn-icon">📊</span>
                        <span class="btn-text">Analisar</span>
                        <span class="btn-desc">Análise detalhada</span>
                    </button>

                    <button class="action-btn warning" id="monitorSignalBtn">
                        <span class="btn-icon">👁️</span>
                        <span class="btn-text">Monitorar</span>
                        <span class="btn-desc">Monitoramento contínuo</span>
                    </button>

                    <button class="action-btn secondary" id="speedTestBtn">
                        <span class="btn-icon">⚡</span>
                        <span class="btn-text">Teste Velocidade</span>
                        <span class="btn-desc">Medir throughput</span>
                    </button>

                    <button class="action-btn danger" id="jamSignalBtn">
                        <span class="btn-icon">🚫</span>
                        <span class="btn-text">Interferir</span>
                        <span class="btn-desc">Teste de interferência</span>
                    </button>
                </div>

                <div class="action-output" id="actionOutput">
                    <div class="output-header">
                        <span>📋 Resultado da Ação</span>
                        <button class="clear-output" id="clearOutput">Limpar</button>
                    </div>
                    <div class="output-content" id="outputContent">
                        <div class="output-line">Selecione uma ação para interagir com o sinal WiFi...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
