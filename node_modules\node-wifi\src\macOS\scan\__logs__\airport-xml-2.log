$ /System/Library/PrivateFrameworks/Apple80211.framework/Versions/Current/Resources/airport --scan --xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<array>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>bc:9f:e4:b0:f8:70</string>
		<key>CAPABILITIES</key>
		<integer>17</integer>
		<key>CHANNEL</key>
		<integer>44</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>44</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AAdlZHVyb2FtAQaYJLBIYGwDASwwFAEAAA+sBAEAAA+sBAEAAA+sASgALRqt
		ARf/////AAAAAAAAAAAAAAAAAAAAAAAAAD0WLAAEAAAAAAAAAAAAAAAAAAAA
		AAAAAH8IBAAIAAAAAEC/DJB5gw+q/wAAqv8AIMAFAAAAAADDAgAp3RgAUPIC
		AQGAAAOkAAAnpAAAQkNeAGIyLwA=
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>1</integer>
			</array>
			<key>IE_KEY_RSN_CAPS</key>
			<dict>
				<key>GTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>MFP_CAPABLE</key>
				<false/>
				<key>MFP_REQUIRED</key>
				<false/>
				<key>NO_PAIRWISE</key>
				<false/>
				<key>PRE_AUTH</key>
				<false/>
				<key>PTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>RSN_CAPABILITIES</key>
				<integer>40</integer>
			</dict>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-89</integer>
		<key>SSID</key>
		<data>
		ZWR1cm9hbQ==
		</data>
		<key>SSID_STR</key>
		<string>eduroam</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>bc:9f:e4:b0:f8:72</string>
		<key>CAPABILITIES</key>
		<integer>1</integer>
		<key>CHANNEL</key>
		<integer>44</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>44</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AAtFVFMtSW52aXRlcwEGmCSwSGBsAwEsLRqtARf/////AAAAAAAAAAAAAAAA
		AAAAAAAAAD0WLAAEAAAAAAAAAAAAAAAAAAAAAAAAAH8IBAAIAAAAAEC/DJB5
		gw+q/wAAqv8AIMAFAAAAAADDAgAp3RgAUPICAQGAAAOkAAAnpAAAQkNeAGIy
		LwA=
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSSI</key>
		<integer>-88</integer>
		<key>SSID</key>
		<data>
		RVRTLUludml0ZXM=
		</data>
		<key>SSID_STR</key>
		<string>ETS-Invites</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>bc:9f:e4:be:52:b2</string>
		<key>CAPABILITIES</key>
		<integer>1</integer>
		<key>CHANNEL</key>
		<integer>157</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<true/>
			<key>HT_OP_MODE</key>
			<integer>1</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>157</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AAtFVFMtSW52aXRlcwEGmCSwSGBsAwGdLRqtARf/////AAAAAAAAAAAAAAAA
		AAAAAAAAAD0WnQAVAAAAAAAAAAAAAAAAAAAAAAAAAH8IBAAIAAAAAEC/DJB5
		gw+q/wAAqv8AIMAFAAAAAADDAgA/3RgAUPICAQGAAAOkAAAnpAAAQkNeAGIy
		LwA=
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSSI</key>
		<integer>-87</integer>
		<key>SSID</key>
		<data>
		RVRTLUludml0ZXM=
		</data>
		<key>SSID_STR</key>
		<string>ETS-Invites</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>bc:9f:e4:be:52:b1</string>
		<key>CAPABILITIES</key>
		<integer>17</integer>
		<key>CHANNEL</key>
		<integer>157</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<true/>
			<key>HT_OP_MODE</key>
			<integer>1</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>157</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AApFVFMtQ2FtcHVzAQaYJLBIYGwDAZ0FBAABAAAwFAEAAA+sBAEAAA+sBAEA
		AA+sASgACwUDAApodi0arQEX/////wAAAAAAAAAAAAAAAAAAAAAAAAA9Fp0A
		FQAAAAAAAAAAAAAAAAAAAAAAAAB/CAQACAAAAABAvwyQeYMPqv8AAKr/ACDA
		BQAAAAAAwwIAP90YAFDyAgEBgAADpAAAJ6QAAEJDXgBiMi8A3QcAC4YBBAgP
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>QBSS_LOAD_IE</key>
		<dict>
			<key>QBSS_AAC</key>
			<integer>30312</integer>
			<key>QBSS_CHAN_UTIL</key>
			<integer>10</integer>
			<key>QBSS_STA_COUNT</key>
			<integer>3</integer>
		</dict>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>1</integer>
			</array>
			<key>IE_KEY_RSN_CAPS</key>
			<dict>
				<key>GTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>MFP_CAPABLE</key>
				<false/>
				<key>MFP_REQUIRED</key>
				<false/>
				<key>NO_PAIRWISE</key>
				<false/>
				<key>PRE_AUTH</key>
				<false/>
				<key>PTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>RSN_CAPABILITIES</key>
				<integer>40</integer>
			</dict>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-87</integer>
		<key>SSID</key>
		<data>
		RVRTLUNhbXB1cw==
		</data>
		<key>SSID_STR</key>
		<string>ETS-Campus</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>bc:9f:e4:be:52:b0</string>
		<key>CAPABILITIES</key>
		<integer>17</integer>
		<key>CHANNEL</key>
		<integer>157</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<true/>
			<key>HT_OP_MODE</key>
			<integer>1</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>157</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AAdlZHVyb2FtAQaYJLBIYGwDAZ0FBAABAAAwFAEAAA+sBAEAAA+sBAEAAA+s
		ASgALRqtARf/////AAAAAAAAAAAAAAAAAAAAAAAAAD0WnQAVAAAAAAAAAAAA
		AAAAAAAAAAAAAH8IBAAIAAAAAEC/DJB5gw+q/wAAqv8AIMAFAAAAAADDAgA/
		3RgAUPICAQGAAAOkAAAnpAAAQkNeAGIyLwDdBwALhgEECA8=
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>1</integer>
			</array>
			<key>IE_KEY_RSN_CAPS</key>
			<dict>
				<key>GTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>MFP_CAPABLE</key>
				<false/>
				<key>MFP_REQUIRED</key>
				<false/>
				<key>NO_PAIRWISE</key>
				<false/>
				<key>PRE_AUTH</key>
				<false/>
				<key>PTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>RSN_CAPABILITIES</key>
				<integer>40</integer>
			</dict>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-87</integer>
		<key>SSID</key>
		<data>
		ZWR1cm9hbQ==
		</data>
		<key>SSID_STR</key>
		<string>eduroam</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>bc:9f:e4:b0:f8:71</string>
		<key>CAPABILITIES</key>
		<integer>17</integer>
		<key>CHANNEL</key>
		<integer>44</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>44</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AApFVFMtQ2FtcHVzAQaYJLBIYGwDASwwFAEAAA+sBAEAAA+sBAEAAA+sASgA
		CwUCAAWhdy0arQEX/////wAAAAAAAAAAAAAAAAAAAAAAAAA9FiwABAAAAAAA
		AAAAAAAAAAAAAAAAAAB/CAQACAAAAABAvwyQeYMPqv8AAKr/ACDABQAAAAAA
		wwIAKd0YAFDyAgEBgAADpAAAJ6QAAEJDXgBiMi8A
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>QBSS_LOAD_IE</key>
		<dict>
			<key>QBSS_AAC</key>
			<integer>30625</integer>
			<key>QBSS_CHAN_UTIL</key>
			<integer>5</integer>
			<key>QBSS_STA_COUNT</key>
			<integer>2</integer>
		</dict>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>1</integer>
			</array>
			<key>IE_KEY_RSN_CAPS</key>
			<dict>
				<key>GTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>MFP_CAPABLE</key>
				<false/>
				<key>MFP_REQUIRED</key>
				<false/>
				<key>NO_PAIRWISE</key>
				<false/>
				<key>PRE_AUTH</key>
				<false/>
				<key>PTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>RSN_CAPABILITIES</key>
				<integer>40</integer>
			</dict>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-87</integer>
		<key>SSID</key>
		<data>
		RVRTLUNhbXB1cw==
		</data>
		<key>SSID_STR</key>
		<string>ETS-Campus</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>80211D_IE</key>
		<dict>
			<key>IE_KEY_80211D_CHAN_INFO_ARRAY</key>
			<array>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>17</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>40</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>17</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>44</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>17</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>48</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>17</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>52</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>56</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>60</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>64</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>100</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>104</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>108</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>112</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>116</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>132</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>136</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>140</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>144</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>24</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>149</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>30</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>153</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>30</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>157</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>30</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>161</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>30</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>165</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>30</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>1</integer>
				</dict>
			</array>
			<key>IE_KEY_80211D_COUNTRY_CODE</key>
			<string>CA</string>
		</dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>APPLE_IE</key>
		<dict>
			<key>APPLE_IE_PRODUCT_ID</key>
			<integer>119</integer>
			<key>APPLE_IE_VERSION</key>
			<integer>1</integer>
			<key>APPLE_IE_WOW_SUPPORTED</key>
			<true/>
			<key>APPLE_IE_WSC_CAP</key>
			<true/>
		</dict>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>24:a0:74:7a:18:81</string>
		<key>CAPABILITIES</key>
		<integer>4369</integer>
		<key>CHANNEL</key>
		<integer>149</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>1040</integer>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>2543</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			////AAAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<false/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>149</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<true/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>1</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<true/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AA5sYWJvQTM0ODQgNUdoegEIjBKYJLBIYGwFBAIDAAAHRkNBICQBESgBESwB
		ETABETQBGDgBGDwBGEABGGQBGGgBGGwBGHABGHQBGIQBGIgBGIwBGJABGJUB
		HpkBHp0BHqEBHqUBHgAgAQAjAhkAMBgBAAAPrAICAAAPrAQAD6wCAQAAD6wC
		AAAtGu8JF////wAAAAAAAAAAAAAAAAAAAAAAAAAAPRaVDQAAAAAAAAAAAAAA
		AAAAAAAAAAAAfwgAAAAAAAAAQL8MslmCD+r/AADq/wAAwAUBmwAAAMMEAgIC
		At0LABfyAQABAQAAAAfdBwADkwF3AyDdDgAX8gcAAQEGJKB0ehiB3QkAEBgC
		AAAcAADdFgBQ8gEBAABQ8gIBAABQ8gIBAABQ8gLdGABQ8gIBAYAAA6QAACek
		AABCQ14AYjIvAEYFAgABAAA=
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>6</integer>
			<integer>9</integer>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>2</integer>
			</array>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>2</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
				<integer>2</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-84</integer>
		<key>SSID</key>
		<data>
		bGFib0EzNDg0IDVHaHo=
		</data>
		<key>SSID_STR</key>
		<string>laboA3484 5Ghz</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260200882</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			6v8AAOr/AAA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>-101</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>1</integer>
		</dict>
		<key>WPA_IE</key>
		<dict>
			<key>IE_KEY_WPA_AUTHSELS</key>
			<array>
				<integer>2</integer>
			</array>
			<key>IE_KEY_WPA_MCIPHER</key>
			<integer>2</integer>
			<key>IE_KEY_WPA_UCIPHERS</key>
			<array>
				<integer>2</integer>
			</array>
			<key>IE_KEY_WPA_VERSION</key>
			<integer>1</integer>
		</dict>
	</dict>
	<dict>
		<key>80211D_IE</key>
		<dict>
			<key>IE_KEY_80211D_CHAN_INFO_ARRAY</key>
			<array>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>23</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>52</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>30</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>100</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>132</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>149</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
			</array>
			<key>IE_KEY_80211D_COUNTRY_CODE</key>
			<string>CA</string>
		</dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>bc:9f:e4:b1:1:b2</string>
		<key>CAPABILITIES</key>
		<integer>1</integer>
		<key>CHANNEL</key>
		<integer>132</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>132</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AAtFVFMtSW52aXRlcwEGmCSwSGBsAwGEBxJDQSAkBBc0BB5kBSSEBCSVBSQg
		AQAjAg8ALRqtARf/////AAAAAAAAAAAAAAAAAAAAAAAAAD0WhAAEAAAAAAAA
		AAAAAAAAAAAAAAAAAH8IBAAIAAAAAEC/DJB5gw+q/wAAqv8AIMAFAAAAAADD
		AgA+3RgAUPICAQGAAAOkAAAnpAAAQkNeAGIyLwA=
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSSI</key>
		<integer>-82</integer>
		<key>SSID</key>
		<data>
		RVRTLUludml0ZXM=
		</data>
		<key>SSID_STR</key>
		<string>ETS-Invites</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>80211D_IE</key>
		<dict>
			<key>IE_KEY_80211D_CHAN_INFO_ARRAY</key>
			<array>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>23</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>52</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>30</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>100</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>132</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>149</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
			</array>
			<key>IE_KEY_80211D_COUNTRY_CODE</key>
			<string>CA</string>
		</dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>bc:9f:e4:b1:1:b1</string>
		<key>CAPABILITIES</key>
		<integer>17</integer>
		<key>CHANNEL</key>
		<integer>132</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>132</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AApFVFMtQ2FtcHVzAQaYJLBIYGwDAYQHEkNBICQEFzQEHmQFJIQEJJUFJCAB
		ACMCDwAwFAEAAA+sBAEAAA+sBAEAAA+sASgACwUFABqhdy0arQEX/////wAA
		AAAAAAAAAAAAAAAAAAAAAAA9FoQABAAAAAAAAAAAAAAAAAAAAAAAAAB/CAQA
		CAAAAABAvwyQeYMPqv8AAKr/ACDABQAAAAAAwwIAPt0YAFDyAgEBgAADpAAA
		J6QAAEJDXgBiMi8A
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>QBSS_LOAD_IE</key>
		<dict>
			<key>QBSS_AAC</key>
			<integer>30625</integer>
			<key>QBSS_CHAN_UTIL</key>
			<integer>26</integer>
			<key>QBSS_STA_COUNT</key>
			<integer>5</integer>
		</dict>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>1</integer>
			</array>
			<key>IE_KEY_RSN_CAPS</key>
			<dict>
				<key>GTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>MFP_CAPABLE</key>
				<false/>
				<key>MFP_REQUIRED</key>
				<false/>
				<key>NO_PAIRWISE</key>
				<false/>
				<key>PRE_AUTH</key>
				<false/>
				<key>PTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>RSN_CAPABILITIES</key>
				<integer>40</integer>
			</dict>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-82</integer>
		<key>SSID</key>
		<data>
		RVRTLUNhbXB1cw==
		</data>
		<key>SSID_STR</key>
		<string>ETS-Campus</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>80211D_IE</key>
		<dict>
			<key>IE_KEY_80211D_CHAN_INFO_ARRAY</key>
			<array>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>23</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>52</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>30</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>100</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>132</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>149</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
			</array>
			<key>IE_KEY_80211D_COUNTRY_CODE</key>
			<string>CA</string>
		</dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>bc:9f:e4:b1:1:b0</string>
		<key>CAPABILITIES</key>
		<integer>17</integer>
		<key>CHANNEL</key>
		<integer>132</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>132</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AAdlZHVyb2FtAQaYJLBIYGwDAYQHEkNBICQEFzQEHmQFJIQEJJUFJCABACMC
		DwAwFAEAAA+sBAEAAA+sBAEAAA+sASgALRqtARf/////AAAAAAAAAAAAAAAA
		AAAAAAAAAD0WhAAEAAAAAAAAAAAAAAAAAAAAAAAAAH8IBAAIAAAAAEC/DJB5
		gw+q/wAAqv8AIMAFAAAAAADDAgA+3RgAUPICAQGAAAOkAAAnpAAAQkNeAGIy
		LwA=
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>1</integer>
			</array>
			<key>IE_KEY_RSN_CAPS</key>
			<dict>
				<key>GTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>MFP_CAPABLE</key>
				<false/>
				<key>MFP_REQUIRED</key>
				<false/>
				<key>NO_PAIRWISE</key>
				<false/>
				<key>PRE_AUTH</key>
				<false/>
				<key>PTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>RSN_CAPABILITIES</key>
				<integer>40</integer>
			</dict>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-82</integer>
		<key>SSID</key>
		<data>
		ZWR1cm9hbQ==
		</data>
		<key>SSID_STR</key>
		<string>eduroam</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>d8:47:32:af:ae:ff</string>
		<key>CAPABILITIES</key>
		<integer>17</integer>
		<key>CHANNEL</key>
		<integer>149</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>1040</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>111</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			//8AAAEAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<false/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>149</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>1</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<true/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AA9UUC1MaW5rX0FGMDBfNUcBCIwSmCSwSGBsAwGVLRpvABf//wAAAQAAAAAA
		AAAAAAAAAAAAAAAAAD0WlQUAAAAAAAAAAAAAAAAAAAAAAAAAADAUAQAAD6wE
		AQAAD6wEAQAAD6wCAAB/CAAACAAAAAAA3RgAUPICAQEAAAOkAAAnpAAAQkNe
		AGIyLwC/DLABwDH6/wwD+v8MA8AFAZsA+v/dkwBQ8gQQSgABEBBEAAECEDsA
		AQMQRwAQOIMwkjCSGIOcd9hHMq+vxBAhAAdUUC1MaW5rECMACkFyY2hlciBD
		NTAQJAADNC4wEEIAAzEuMBBUAAgABgBQ8gQAARARACBBQzEyMDAgV2lyZWxl
		c3MgRHVhbCBCYW5kIFJvdXRlchAIAAIhDBA8AAECEEkABgA3KgABIN0HAAxD
		AAAAAN0HAAznAAAAAA==
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>6</integer>
			<integer>9</integer>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>2</integer>
			</array>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-67</integer>
		<key>SSID</key>
		<data>
		VFAtTGlua19BRjAwXzVH
		</data>
		<key>SSID_STR</key>
		<string>TP-Link_AF00_5G</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>834666928</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			+v8MA/r/DAM=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>-6</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>-101</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>1</integer>
		</dict>
		<key>WPS_PROB_RESP_IE</key>
		<dict>
			<key>IE_KEY_WPS_CFG_METHODS</key>
			<integer>8460</integer>
			<key>IE_KEY_WPS_DEV_NAME</key>
			<string>AC1200 Wireless Dual Band Router</string>
			<key>IE_KEY_WPS_DEV_NAME_DATA</key>
			<data>
			QUMxMjAwIFdpcmVsZXNzIER1YWwgQmFuZCBSb3V0ZXI=
			</data>
			<key>IE_KEY_WPS_MANUFACTURER</key>
			<string>TP-Link</string>
			<key>IE_KEY_WPS_MODEL_NAME</key>
			<string>Archer C50</string>
			<key>IE_KEY_WPS_MODEL_NUM</key>
			<string>4.0</string>
			<key>IE_KEY_WPS_PRIMARY_DEV_TYPE</key>
			<dict>
				<key>WPS_DEV_TYPE_CAT</key>
				<integer>6</integer>
				<key>WPS_DEV_TYPE_OUI</key>
				<data>
				AFDyBA==
				</data>
				<key>WPS_DEV_TYPE_SUB_CAT</key>
				<integer>1</integer>
			</dict>
			<key>IE_KEY_WPS_RESP_TYPE</key>
			<integer>3</integer>
			<key>IE_KEY_WPS_RF_BANDS</key>
			<integer>2</integer>
			<key>IE_KEY_WPS_SC_STATE</key>
			<integer>2</integer>
			<key>IE_KEY_WPS_SERIAL_NUM</key>
			<string>1.0</string>
			<key>IE_KEY_WPS_UUID_E</key>
			<data>
			OIMwkjCSGIOcd9hHMq+vxA==
			</data>
		</dict>
	</dict>
	<dict>
		<key>80211D_IE</key>
		<dict>
			<key>IE_KEY_80211D_CHAN_INFO_ARRAY</key>
			<array>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>23</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>52</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>30</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>100</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>132</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>149</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
			</array>
			<key>IE_KEY_80211D_COUNTRY_CODE</key>
			<string>CA</string>
		</dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>f4:2e:7f:18:c7:72</string>
		<key>CAPABILITIES</key>
		<integer>1</integer>
		<key>CHANNEL</key>
		<integer>136</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>136</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AAtFVFMtSW52aXRlcwEGmCSwSGBsAwGIBQQAAQAABxJDQSAkBBc0BB5kBSSE
		BCSVBSQgAQAjAg8ALRqtARf/////AAAAAAAAAAAAAAAAAAAAAAAAAD0WiAAE
		AAAAAAAAAAAAAAAAAAAAAAAAAH8IBAAIAAAAAEC/DJB5gw+q/wAAqv8AIMAF
		AAAAAADDAgA+3RgAUPICAQGAAAOkAAAnpAAAQkNeAGIyLwDdBwALhgEECA8=
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSSI</key>
		<integer>-66</integer>
		<key>SSID</key>
		<data>
		RVRTLUludml0ZXM=
		</data>
		<key>SSID_STR</key>
		<string>ETS-Invites</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>80211D_IE</key>
		<dict>
			<key>IE_KEY_80211D_CHAN_INFO_ARRAY</key>
			<array>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>23</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>52</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>30</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>100</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>132</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>149</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
			</array>
			<key>IE_KEY_80211D_COUNTRY_CODE</key>
			<string>CA</string>
		</dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>f4:2e:7f:18:c7:71</string>
		<key>CAPABILITIES</key>
		<integer>17</integer>
		<key>CHANNEL</key>
		<integer>136</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>136</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AApFVFMtQ2FtcHVzAQaYJLBIYGwDAYgFBAABAAAHEkNBICQEFzQEHmQFJIQE
		JJUFJCABACMCDwAwFAEAAA+sBAEAAA+sBAEAAA+sASgACwUCAAHZeC0arQEX
		/////wAAAAAAAAAAAAAAAAAAAAAAAAA9FogABAAAAAAAAAAAAAAAAAAAAAAA
		AAB/CAQACAAAAABAvwyQeYMPqv8AAKr/ACDABQAAAAAAwwIAPt0YAFDyAgEB
		gAADpAAAJ6QAAEJDXgBiMi8A3QcAC4YBBAgP
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>QBSS_LOAD_IE</key>
		<dict>
			<key>QBSS_AAC</key>
			<integer>30937</integer>
			<key>QBSS_CHAN_UTIL</key>
			<integer>1</integer>
			<key>QBSS_STA_COUNT</key>
			<integer>2</integer>
		</dict>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>1</integer>
			</array>
			<key>IE_KEY_RSN_CAPS</key>
			<dict>
				<key>GTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>MFP_CAPABLE</key>
				<false/>
				<key>MFP_REQUIRED</key>
				<false/>
				<key>NO_PAIRWISE</key>
				<false/>
				<key>PRE_AUTH</key>
				<false/>
				<key>PTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>RSN_CAPABILITIES</key>
				<integer>40</integer>
			</dict>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-66</integer>
		<key>SSID</key>
		<data>
		RVRTLUNhbXB1cw==
		</data>
		<key>SSID_STR</key>
		<string>ETS-Campus</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>80211D_IE</key>
		<dict>
			<key>IE_KEY_80211D_CHAN_INFO_ARRAY</key>
			<array>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>23</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>52</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>30</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>100</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>132</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>IE_KEY_80211D_FIRST_CHANNEL</key>
					<integer>149</integer>
					<key>IE_KEY_80211D_MAX_POWER</key>
					<integer>36</integer>
					<key>IE_KEY_80211D_NUM_CHANNELS</key>
					<integer>5</integer>
				</dict>
			</array>
			<key>IE_KEY_80211D_COUNTRY_CODE</key>
			<string>CA</string>
		</dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>f4:2e:7f:18:c7:70</string>
		<key>CAPABILITIES</key>
		<integer>17</integer>
		<key>CHANNEL</key>
		<integer>136</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>18</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			/////wAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>136</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AAdlZHVyb2FtAQaYJLBIYGwDAYgFBAABAAAHEkNBICQEFzQEHmQFJIQEJJUF
		JCABACMCDwAwFAEAAA+sBAEAAA+sBAEAAA+sASgALRqtARf/////AAAAAAAA
		AAAAAAAAAAAAAAAAAD0WiAAEAAAAAAAAAAAAAAAAAAAAAAAAAH8IBAAIAAAA
		AEC/DJB5gw+q/wAAqv8AIMAFAAAAAADDAgA+3RgAUPICAQGAAAOkAAAnpAAA
		QkNeAGIyLwDdBwALhgEECA8=
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>1</integer>
			</array>
			<key>IE_KEY_RSN_CAPS</key>
			<dict>
				<key>GTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>MFP_CAPABLE</key>
				<false/>
				<key>MFP_REQUIRED</key>
				<false/>
				<key>NO_PAIRWISE</key>
				<false/>
				<key>PRE_AUTH</key>
				<false/>
				<key>PTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>RSN_CAPABILITIES</key>
				<integer>40</integer>
			</dict>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-66</integer>
		<key>SSID</key>
		<data>
		ZWR1cm9hbQ==
		</data>
		<key>SSID_STR</key>
		<string>eduroam</string>
		<key>VHT_CAPS</key>
		<dict>
			<key>INFO</key>
			<integer>260274576</integer>
			<key>SUPPORTED_MCS_SET</key>
			<data>
			qv8AAKr/ACA=
			</data>
		</dict>
		<key>VHT_OP</key>
		<dict>
			<key>BASIC_MCS_SET</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG0</key>
			<integer>0</integer>
			<key>CHANNEL_CENTER_FREQUENCY_SEG1</key>
			<integer>0</integer>
			<key>CHANNEL_WIDTH</key>
			<integer>0</integer>
		</dict>
	</dict>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>0:25:9c:da:c6:38</string>
		<key>CAPABILITIES</key>
		<integer>1073</integer>
		<key>CHANNEL</key>
		<integer>8</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>10</integer>
		<key>IE</key>
		<data>
		AARNQVJTAQiChIuWDBIYJAMBCDAUAQAAD6wEAQAAD6wEAQAAD6wCAAAqAQAy
		BDBIYGzdCQADfwEBAAD/f90KAAN/BAEAAgBAAN10AFDyBBBKAAEQEEQAAQIQ
		OwABAxBHABAAAAAAAAAQAAAAACWc2sY4ECEADExpbmtzeXMgSW5jLhAjAAdX
		UlQ1NEcyECQAB3YxLjUuMDEQQgABMBBUAAgABgBQ8gQAARARAAdXUlQ1NEcy
		EAgAAgCEEDwAAQE=
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>1</integer>
			<integer>2</integer>
			<integer>5</integer>
			<integer>11</integer>
			<integer>6</integer>
			<integer>9</integer>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>2</integer>
			</array>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-65</integer>
		<key>SSID</key>
		<data>
		TUFSUw==
		</data>
		<key>SSID_STR</key>
		<string>MARS</string>
		<key>WPS_PROB_RESP_IE</key>
		<dict>
			<key>IE_KEY_WPS_CFG_METHODS</key>
			<integer>132</integer>
			<key>IE_KEY_WPS_DEV_NAME</key>
			<string>WRT54G2</string>
			<key>IE_KEY_WPS_DEV_NAME_DATA</key>
			<data>
			V1JUNTRHMg==
			</data>
			<key>IE_KEY_WPS_MANUFACTURER</key>
			<string>Linksys Inc.</string>
			<key>IE_KEY_WPS_MODEL_NAME</key>
			<string>WRT54G2</string>
			<key>IE_KEY_WPS_MODEL_NUM</key>
			<string>v1.5.01</string>
			<key>IE_KEY_WPS_PRIMARY_DEV_TYPE</key>
			<dict>
				<key>WPS_DEV_TYPE_CAT</key>
				<integer>6</integer>
				<key>WPS_DEV_TYPE_OUI</key>
				<data>
				AFDyBA==
				</data>
				<key>WPS_DEV_TYPE_SUB_CAT</key>
				<integer>1</integer>
			</dict>
			<key>IE_KEY_WPS_RESP_TYPE</key>
			<integer>3</integer>
			<key>IE_KEY_WPS_RF_BANDS</key>
			<integer>1</integer>
			<key>IE_KEY_WPS_SC_STATE</key>
			<integer>2</integer>
			<key>IE_KEY_WPS_SERIAL_NUM</key>
			<string>0</string>
			<key>IE_KEY_WPS_UUID_E</key>
			<data>
			AAAAAAAAEAAAAAAlnNrGOA==
			</data>
		</dict>
	</dict>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>f4:2e:7f:18:c7:62</string>
		<key>CAPABILITIES</key>
		<integer>1057</integer>
		<key>CHANNEL</key>
		<integer>1</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>10</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			//8AAAAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>1</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AAtFVFMtSW52aXRlcwEGmCSwSGBsAwEBKgEELRqtARf//wAAAAAAAAAAAAAA
		AAAAAAAAAAAAAD0WAQAEAAAAAAAAAAAAAAAAAAAAAAAAAEoOFAAKACwByAAU
		AAUAGQB/CAUACAAAAABA3R4AkEwECL8MkAGAA///AAD//wAAwAUAAAAAAMMC
		ADbdGABQ8gIBAYAAA6QAACekAABCQ14AYjIvAA==
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSSI</key>
		<integer>-64</integer>
		<key>SSID</key>
		<data>
		RVRTLUludml0ZXM=
		</data>
		<key>SSID_STR</key>
		<string>ETS-Invites</string>
	</dict>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>f4:2e:7f:18:c7:61</string>
		<key>CAPABILITIES</key>
		<integer>1041</integer>
		<key>CHANNEL</key>
		<integer>1</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>10</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			//8AAAAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>1</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AApFVFMtQ2FtcHVzAQaYJLBIYGwDAQEqAQQwFAEAAA+sBAEAAA+sBAEAAA+s
		ASgACwUBAALZeC0arQEX//8AAAAAAAAAAAAAAAAAAAAAAAAAAAA9FgEABAAA
		AAAAAAAAAAAAAAAAAAAAAABKDhQACgAsAcgAFAAFABkAfwgFAAgAAAAAQN0e
		AJBMBAi/DJABgAP//wAA//8AAMAFAAAAAADDAgA23RgAUPICAQGAAAOkAAAn
		pAAAQkNeAGIyLwA=
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>QBSS_LOAD_IE</key>
		<dict>
			<key>QBSS_AAC</key>
			<integer>30937</integer>
			<key>QBSS_CHAN_UTIL</key>
			<integer>2</integer>
			<key>QBSS_STA_COUNT</key>
			<integer>1</integer>
		</dict>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>1</integer>
			</array>
			<key>IE_KEY_RSN_CAPS</key>
			<dict>
				<key>GTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>MFP_CAPABLE</key>
				<false/>
				<key>MFP_REQUIRED</key>
				<false/>
				<key>NO_PAIRWISE</key>
				<false/>
				<key>PRE_AUTH</key>
				<false/>
				<key>PTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>RSN_CAPABILITIES</key>
				<integer>40</integer>
			</dict>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-64</integer>
		<key>SSID</key>
		<data>
		RVRTLUNhbXB1cw==
		</data>
		<key>SSID_STR</key>
		<string>ETS-Campus</string>
	</dict>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>AP_MODE</key>
		<integer>2</integer>
		<key>BEACON_INT</key>
		<integer>100</integer>
		<key>BSSID</key>
		<string>f4:2e:7f:18:c7:60</string>
		<key>CAPABILITIES</key>
		<integer>1073</integer>
		<key>CHANNEL</key>
		<integer>1</integer>
		<key>CHANNEL_FLAGS</key>
		<integer>10</integer>
		<key>EXT_CAPS</key>
		<dict>
			<key>BSS_TRANS_MGMT</key>
			<integer>1</integer>
		</dict>
		<key>HT_CAPS_IE</key>
		<dict>
			<key>AMPDU_PARAMS</key>
			<integer>23</integer>
			<key>ASEL_CAPS</key>
			<integer>0</integer>
			<key>CAPS</key>
			<integer>429</integer>
			<key>EXT_CAPS</key>
			<integer>0</integer>
			<key>MCS_SET</key>
			<data>
			//8AAAAAAAAAAAAAAAAAAA==
			</data>
			<key>TXBF_CAPS</key>
			<integer>0</integer>
		</dict>
		<key>HT_IE</key>
		<dict>
			<key>HT_BASIC_MCS_SET</key>
			<data>
			AAAAAAAAAAAAAAAAAAAAAA==
			</data>
			<key>HT_DUAL_BEACON</key>
			<false/>
			<key>HT_DUAL_CTS_PROT</key>
			<false/>
			<key>HT_LSIG_TXOP_PROT_FULL</key>
			<false/>
			<key>HT_NON_GF_STAS_PRESENT</key>
			<true/>
			<key>HT_OBSS_NON_HT_STAS_PRESENT</key>
			<false/>
			<key>HT_OP_MODE</key>
			<integer>0</integer>
			<key>HT_PCO_ACTIVE</key>
			<false/>
			<key>HT_PCO_PHASE</key>
			<false/>
			<key>HT_PRIMARY_CHAN</key>
			<integer>1</integer>
			<key>HT_PSMP_STAS_ONLY</key>
			<false/>
			<key>HT_RIFS_MODE</key>
			<false/>
			<key>HT_SECONDARY_BEACON</key>
			<false/>
			<key>HT_SECONDARY_CHAN_OFFSET</key>
			<integer>0</integer>
			<key>HT_SERVICE_INT</key>
			<integer>0</integer>
			<key>HT_STA_CHAN_WIDTH</key>
			<false/>
			<key>HT_TX_BURST_LIMIT</key>
			<false/>
		</dict>
		<key>IE</key>
		<data>
		AAdlZHVyb2FtAQaYJLBIYGwDAQEqAQQwFAEAAA+sBAEAAA+sBAEAAA+sASgA
		LRqtARf//wAAAAAAAAAAAAAAAAAAAAAAAAAAAD0WAQAEAAAAAAAAAAAAAAAA
		AAAAAAAAAEoOFAAKACwByAAUAAUAGQB/CAUACAAAAABA3R4AkEwECL8MkAGA
		A///AAD//wAAwAUAAAAAAMMCADbdGABQ8gIBAYAAA6QAACekAABCQ14AYjIv
		AA==
		</data>
		<key>NOISE</key>
		<integer>-92</integer>
		<key>RATES</key>
		<array>
			<integer>12</integer>
			<integer>18</integer>
			<integer>24</integer>
			<integer>36</integer>
			<integer>48</integer>
			<integer>54</integer>
		</array>
		<key>RSN_IE</key>
		<dict>
			<key>IE_KEY_RSN_AUTHSELS</key>
			<array>
				<integer>1</integer>
			</array>
			<key>IE_KEY_RSN_CAPS</key>
			<dict>
				<key>GTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>MFP_CAPABLE</key>
				<false/>
				<key>MFP_REQUIRED</key>
				<false/>
				<key>NO_PAIRWISE</key>
				<false/>
				<key>PRE_AUTH</key>
				<false/>
				<key>PTKSA_REPLAY_COUNTERS</key>
				<integer>4</integer>
				<key>RSN_CAPABILITIES</key>
				<integer>40</integer>
			</dict>
			<key>IE_KEY_RSN_MCIPHER</key>
			<integer>4</integer>
			<key>IE_KEY_RSN_UCIPHERS</key>
			<array>
				<integer>4</integer>
			</array>
			<key>IE_KEY_RSN_VERSION</key>
			<integer>1</integer>
		</dict>
		<key>RSSI</key>
		<integer>-64</integer>
		<key>SSID</key>
		<data>
		ZWR1cm9hbQ==
		</data>
		<key>SSID_STR</key>
		<string>eduroam</string>
	</dict>
	<dict>
		<key>AGE</key>
		<integer>0</integer>
		<key>APPLE_SWAP</key>
		<true/>
		<key>APPLE_SWAP_IE</key>
		<dict>
			<key>SWAP_IE_FEATURES</key>
			<data>
			AQAA
			</data>
			<key>SWAP_IE_FEATURE_INTERNET_CONNECTION_SHARING_ENABLED</key>
			<true/>
			<key>SWAP_IE_FEATURE_VERSION</key>
			<integer>1</integer>
			<key>SWAP_IE_MODEL_IDENTIFIER</key>
			<string>iMac15,1
