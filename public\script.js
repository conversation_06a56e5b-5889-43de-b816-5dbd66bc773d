class WiFiRadar {
    constructor() {
        this.networks = [];
        this.ws = null;
        this.isScanning = false;
        this.radarRadius = 200; // Raio máximo do radar em pixels
        
        this.initializeElements();
        this.setupEventListeners();
        this.connectWebSocket();
    }

    initializeElements() {
        this.scanBtn = document.getElementById('scanBtn');
        this.status = document.getElementById('status');
        this.radarScreen = document.getElementById('radarScreen');
        this.networksList = document.getElementById('networksList');
        this.modal = document.getElementById('networkModal');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalBody = document.getElementById('modalBody');
        this.modalClose = document.getElementById('modalClose');
    }

    setupEventListeners() {
        this.scanBtn.addEventListener('click', () => this.scanNetworks());
        this.modalClose.addEventListener('click', () => this.closeModal());
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) this.closeModal();
        });
        
        // Fechar modal com ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.closeModal();
            }
        });
    }

    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;
        
        try {
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = () => {
                console.log('WebSocket conectado');
                this.updateStatus('Conectado', 'connected');
            };
            
            this.ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'networks') {
                    this.updateNetworks(data.networks, data.simulated);
                }
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket desconectado');
                this.updateStatus('Desconectado', '');
                // Tentar reconectar após 3 segundos
                setTimeout(() => this.connectWebSocket(), 3000);
            };
            
            this.ws.onerror = (error) => {
                console.error('Erro WebSocket:', error);
                this.updateStatus('Erro de conexão', '');
            };
        } catch (error) {
            console.error('Erro ao conectar WebSocket:', error);
            this.updateStatus('Erro de conexão', '');
        }
    }

    updateStatus(message, className = '') {
        this.status.textContent = message;
        this.status.className = `status ${className}`;
    }

    async scanNetworks() {
        if (this.isScanning) return;
        
        this.isScanning = true;
        this.scanBtn.classList.add('scanning');
        this.scanBtn.innerHTML = '<span class="btn-icon">🔄</span>Escaneando...';
        this.updateStatus('Escaneando redes...', 'scanning');
        
        try {
            // Enviar comando de scan via WebSocket se conectado
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({ type: 'scan' }));
            } else {
                // Fallback para API REST
                const response = await fetch('/api/scan');
                const data = await response.json();
                
                if (data.success) {
                    this.updateNetworks(data.networks, data.simulated);
                } else {
                    throw new Error(data.error || 'Erro ao escanear redes');
                }
            }
        } catch (error) {
            console.error('Erro no escaneamento:', error);
            this.updateStatus('Erro no escaneamento', '');
            this.showError('Erro ao escanear redes WiFi: ' + error.message);
        } finally {
            setTimeout(() => {
                this.isScanning = false;
                this.scanBtn.classList.remove('scanning');
                this.scanBtn.innerHTML = '<span class="btn-icon">🔄</span>Escanear';
            }, 1000);
        }
    }

    updateNetworks(networks, isSimulated = false) {
        this.networks = networks;
        this.renderRadar();
        this.renderNetworksList();
        
        const statusMessage = isSimulated 
            ? `${networks.length} redes simuladas (demonstração)`
            : `${networks.length} redes encontradas`;
        
        this.updateStatus(statusMessage, 'connected');
        
        // Mostrar notificação se estiver usando dados simulados
        if (isSimulated && networks.length > 0) {
            this.showSimulationNotice();
        }
    }

    renderRadar() {
        // Remover pontos existentes
        const existingPoints = this.radarScreen.querySelectorAll('.router-point');
        existingPoints.forEach(point => point.remove());

        this.networks.forEach((network, index) => {
            const point = this.createRouterPoint(network, index);
            this.radarScreen.appendChild(point);
        });
    }

    createRouterPoint(network, index) {
        const point = document.createElement('div');
        point.className = `router-point ${this.getSignalClass(network.rssi)}`;
        
        // Calcular posição baseada no RSSI
        const distance = this.calculateRadarDistance(network.rssi);
        const angle = (index * 360 / this.networks.length) * (Math.PI / 180);
        
        const x = 250 + distance * Math.cos(angle);
        const y = 250 + distance * Math.sin(angle);
        
        point.style.left = `${x - 5}px`;
        point.style.top = `${y - 5}px`;
        
        // Adicionar label com SSID
        const label = document.createElement('div');
        label.className = 'router-label';
        label.textContent = network.ssid || 'Rede Oculta';
        point.appendChild(label);
        
        // Adicionar evento de clique
        point.addEventListener('click', () => this.showNetworkDetails(network));
        
        return point;
    }

    calculateRadarDistance(rssi) {
        // Mapear RSSI para distância no radar (0 a radarRadius)
        // RSSI típico: -30 (muito forte) a -90 (muito fraco)
        const minRssi = -90;
        const maxRssi = -30;
        
        // Normalizar RSSI para 0-1
        const normalizedRssi = Math.max(0, Math.min(1, (rssi - minRssi) / (maxRssi - minRssi)));
        
        // Inverter (sinal mais forte = mais próximo do centro)
        const distance = (1 - normalizedRssi) * this.radarRadius;
        
        return Math.max(20, distance); // Mínimo de 20px do centro
    }

    getSignalClass(rssi) {
        if (rssi > -50) return 'strong';
        if (rssi > -70) return 'medium';
        if (rssi > -85) return 'weak';
        return 'very-weak';
    }

    getSignalStrength(rssi) {
        if (rssi > -50) return { text: 'Excelente', percentage: 100 };
        if (rssi > -60) return { text: 'Muito Bom', percentage: 80 };
        if (rssi > -70) return { text: 'Bom', percentage: 60 };
        if (rssi > -80) return { text: 'Fraco', percentage: 40 };
        return { text: 'Muito Fraco', percentage: 20 };
    }

    renderNetworksList() {
        if (this.networks.length === 0) {
            this.networksList.innerHTML = `
                <div class="no-networks">
                    <p>Nenhuma rede WiFi encontrada</p>
                </div>
            `;
            return;
        }

        // Ordenar por força do sinal
        const sortedNetworks = [...this.networks].sort((a, b) => b.rssi - a.rssi);

        this.networksList.innerHTML = sortedNetworks.map(network => `
            <div class="network-item" onclick="wifiRadar.showNetworkDetails(${JSON.stringify(network).replace(/"/g, '&quot;')})">
                <div class="network-name">${network.ssid || 'Rede Oculta'}</div>
                <div class="network-details">
                    <span>${network.rssi} dBm</span>
                    <span>${network.frequencyBand}</span>
                    <span>${network.security}</span>
                </div>
            </div>
        `).join('');
    }

    showNetworkDetails(network) {
        const signalStrength = this.getSignalStrength(network.rssi);
        
        this.modalTitle.textContent = network.ssid || 'Rede Oculta';
        
        this.modalBody.innerHTML = `
            <div class="detail-row">
                <span class="detail-label">SSID:</span>
                <span class="detail-value">${network.ssid || 'Oculto'}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">BSSID:</span>
                <span class="detail-value">${network.bssid}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Força do Sinal:</span>
                <div class="detail-value signal-strength">
                    <span>${network.rssi} dBm (${signalStrength.text})</span>
                    <div class="signal-bar">
                        <div class="signal-fill ${this.getSignalClass(network.rssi)}" 
                             style="width: ${signalStrength.percentage}%; background: ${this.getSignalColor(network.rssi)};"></div>
                    </div>
                </div>
            </div>
            <div class="detail-row">
                <span class="detail-label">Canal:</span>
                <span class="detail-value">${network.channel}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Frequência:</span>
                <span class="detail-value">${network.frequencyBand}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Segurança:</span>
                <span class="detail-value">${network.security}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Distância Estimada:</span>
                <span class="detail-value">${network.distance} metros</span>
            </div>
            ${network.quality ? `
            <div class="detail-row">
                <span class="detail-label">Qualidade:</span>
                <span class="detail-value">${network.quality}%</span>
            </div>
            ` : ''}
        `;
        
        this.modal.classList.add('show');
    }

    getSignalColor(rssi) {
        if (rssi > -50) return '#00ff41';
        if (rssi > -70) return '#ffff00';
        if (rssi > -85) return '#ff6600';
        return '#ff0000';
    }

    closeModal() {
        this.modal.classList.remove('show');
    }

    showSimulationNotice() {
        // Criar notificação de simulação
        const noticeDiv = document.createElement('div');
        noticeDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #00ff41, #00cc33);
            color: #000;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 255, 65, 0.3);
            z-index: 10000;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
            font-weight: 600;
        `;
        noticeDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.2em;">🎯</span>
                <div>
                    <div>Modo Demonstração Ativo</div>
                    <div style="font-size: 0.8em; opacity: 0.8; margin-top: 2px;">Exibindo redes WiFi simuladas para visualização</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(noticeDiv);
        
        // Remover após 8 segundos
        setTimeout(() => {
            noticeDiv.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => noticeDiv.remove(), 300);
        }, 8000);
    }

    showError(message) {
        // Criar notificação de erro
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
            z-index: 10000;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
        `;
        errorDiv.textContent = message;
        
        document.body.appendChild(errorDiv);
        
        // Remover após 5 segundos
        setTimeout(() => {
            errorDiv.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => errorDiv.remove(), 300);
        }, 5000);
    }
}

// Adicionar animações CSS dinamicamente
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Inicializar aplicação
const wifiRadar = new WiFiRadar();

// Expor globalmente para uso nos event handlers inline
window.wifiRadar = wifiRadar;
