class WiFiRadar {
    constructor() {
        this.networks = [];
        this.ws = null;
        this.isScanning = false;
        this.radarRadius = 200; // Raio máximo do radar em pixels
        
        this.initializeElements();
        this.setupEventListeners();
        this.connectWebSocket();
    }

    initializeElements() {
        this.scanBtn = document.getElementById('scanBtn');
        this.status = document.getElementById('status');
        this.radarScreen = document.getElementById('radarScreen');
        this.networksList = document.getElementById('networksList');
        this.modal = document.getElementById('networkModal');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalBody = document.getElementById('modalBody');
        this.modalClose = document.getElementById('modalClose');
        this.modalTerminal = document.getElementById('modalTerminal');
        this.terminalContent = document.getElementById('terminalContent');
        this.terminalInput = document.getElementById('terminalInput');
        this.terminalHelp = document.getElementById('terminalHelp');
        this.terminalClear = document.getElementById('terminalClear');

        this.currentNetwork = null;
        this.terminalHistory = [];
        this.historyIndex = -1;
    }

    setupEventListeners() {
        this.scanBtn.addEventListener('click', () => this.scanNetworks());
        this.modalClose.addEventListener('click', () => this.closeModal());
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) this.closeModal();
        });

        // Terminal event listeners
        this.terminalInput.addEventListener('keydown', (e) => this.handleTerminalInput(e));
        this.terminalHelp.addEventListener('click', () => this.executeTerminalCommand('help'));
        this.terminalClear.addEventListener('click', () => this.executeTerminalCommand('clear'));

        // Fechar modal com ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.closeModal();
            }
        });
    }

    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;
        
        try {
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = () => {
                console.log('WebSocket conectado');
                this.updateStatus('Conectado', 'connected');
            };
            
            this.ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'networks') {
                    this.updateNetworks(data.networks, data.simulated);
                }
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket desconectado');
                this.updateStatus('Desconectado', '');
                // Tentar reconectar após 3 segundos
                setTimeout(() => this.connectWebSocket(), 3000);
            };
            
            this.ws.onerror = (error) => {
                console.error('Erro WebSocket:', error);
                this.updateStatus('Erro de conexão', '');
            };
        } catch (error) {
            console.error('Erro ao conectar WebSocket:', error);
            this.updateStatus('Erro de conexão', '');
        }
    }

    updateStatus(message, className = '') {
        this.status.textContent = message;
        this.status.className = `status ${className}`;
    }

    async scanNetworks() {
        if (this.isScanning) return;
        
        this.isScanning = true;
        this.scanBtn.classList.add('scanning');
        this.scanBtn.innerHTML = '<span class="btn-icon">🔄</span>Escaneando...';
        this.updateStatus('Escaneando redes...', 'scanning');
        
        try {
            // Enviar comando de scan via WebSocket se conectado
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({ type: 'scan' }));
            } else {
                // Fallback para API REST
                const response = await fetch('/api/scan');
                const data = await response.json();
                
                if (data.success) {
                    this.updateNetworks(data.networks, data.simulated);
                } else {
                    throw new Error(data.error || 'Erro ao escanear redes');
                }
            }
        } catch (error) {
            console.error('Erro no escaneamento:', error);
            this.updateStatus('Erro no escaneamento', '');
            this.showError('Erro ao escanear redes WiFi: ' + error.message);
        } finally {
            setTimeout(() => {
                this.isScanning = false;
                this.scanBtn.classList.remove('scanning');
                this.scanBtn.innerHTML = '<span class="btn-icon">🔄</span>Escanear';
            }, 1000);
        }
    }

    updateNetworks(networks, isSimulated = false) {
        this.networks = networks;
        this.renderRadar();
        this.renderNetworksList();
        
        const statusMessage = isSimulated 
            ? `${networks.length} redes simuladas (demonstração)`
            : `${networks.length} redes encontradas`;
        
        this.updateStatus(statusMessage, 'connected');
        
        // Mostrar notificação se estiver usando dados simulados
        if (isSimulated && networks.length > 0) {
            this.showSimulationNotice();
        }
    }

    renderRadar() {
        // Remover pontos existentes
        const existingPoints = this.radarScreen.querySelectorAll('.router-point');
        existingPoints.forEach(point => point.remove());

        this.networks.forEach((network, index) => {
            const point = this.createRouterPoint(network, index);
            this.radarScreen.appendChild(point);
        });
    }

    createRouterPoint(network, index) {
        const point = document.createElement('div');
        point.className = `router-point ${this.getSignalClass(network.rssi)}`;

        // Calcular posição baseada apenas na distância (RSSI)
        // Todos os pontos ficam alinhados em círculos concêntricos
        const distance = this.calculateRadarDistance(network.rssi);

        // Distribuir pontos uniformemente em círculos baseados na distância
        const pointsAtSameDistance = this.networks.filter(n =>
            Math.abs(this.calculateRadarDistance(n.rssi) - distance) < 10
        );
        const indexAtDistance = pointsAtSameDistance.findIndex(n => n.bssid === network.bssid);
        const totalAtDistance = pointsAtSameDistance.length;

        // Calcular ângulo para distribuição uniforme
        const angle = (indexAtDistance * 360 / Math.max(totalAtDistance, 8)) * (Math.PI / 180);

        const x = 250 + distance * Math.cos(angle);
        const y = 250 + distance * Math.sin(angle);

        point.style.left = `${x - 5}px`;
        point.style.top = `${y - 5}px`;

        // Adicionar label com SSID e distância
        const label = document.createElement('div');
        label.className = 'router-label';
        label.innerHTML = `
            <div class="label-ssid">${network.ssid || 'Rede Oculta'}</div>
            <div class="label-distance">${network.distance}m</div>
        `;
        point.appendChild(label);

        // Adicionar evento de clique
        point.addEventListener('click', () => this.showNetworkDetails(network));

        return point;
    }

    calculateRadarDistance(rssi) {
        // Mapear RSSI para distância no radar (0 a radarRadius)
        // RSSI típico: -30 (muito forte) a -90 (muito fraco)
        const minRssi = -90;
        const maxRssi = -30;
        
        // Normalizar RSSI para 0-1
        const normalizedRssi = Math.max(0, Math.min(1, (rssi - minRssi) / (maxRssi - minRssi)));
        
        // Inverter (sinal mais forte = mais próximo do centro)
        const distance = (1 - normalizedRssi) * this.radarRadius;
        
        return Math.max(20, distance); // Mínimo de 20px do centro
    }

    getSignalClass(rssi) {
        if (rssi > -50) return 'strong';
        if (rssi > -70) return 'medium';
        if (rssi > -85) return 'weak';
        return 'very-weak';
    }

    getSignalStrength(rssi) {
        if (rssi > -50) return { text: 'Excelente', percentage: 100 };
        if (rssi > -60) return { text: 'Muito Bom', percentage: 80 };
        if (rssi > -70) return { text: 'Bom', percentage: 60 };
        if (rssi > -80) return { text: 'Fraco', percentage: 40 };
        return { text: 'Muito Fraco', percentage: 20 };
    }

    renderNetworksList() {
        if (this.networks.length === 0) {
            this.networksList.innerHTML = `
                <div class="no-networks">
                    <p>Nenhuma rede WiFi encontrada</p>
                </div>
            `;
            return;
        }

        // Ordenar por força do sinal
        const sortedNetworks = [...this.networks].sort((a, b) => b.rssi - a.rssi);

        this.networksList.innerHTML = sortedNetworks.map(network => `
            <div class="network-item" onclick="wifiRadar.showNetworkDetails(${JSON.stringify(network).replace(/"/g, '&quot;')})">
                <div class="network-name">${network.ssid || 'Rede Oculta'}</div>
                <div class="network-details">
                    <span>${network.rssi} dBm</span>
                    <span>${network.frequencyBand}</span>
                    <span>${network.security}</span>
                </div>
            </div>
        `).join('');
    }

    showNetworkDetails(network) {
        const signalStrength = this.getSignalStrength(network.rssi);
        this.currentNetwork = network;

        this.modalTitle.textContent = network.ssid || 'Rede Oculta';

        this.modalBody.innerHTML = `
            <div class="detail-row">
                <span class="detail-label">SSID:</span>
                <span class="detail-value">${network.ssid || 'Oculto'}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">BSSID:</span>
                <span class="detail-value">${network.bssid}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Força do Sinal:</span>
                <div class="detail-value signal-strength">
                    <span>${network.rssi} dBm (${signalStrength.text})</span>
                    <div class="signal-bar">
                        <div class="signal-fill ${this.getSignalClass(network.rssi)}"
                             style="width: ${signalStrength.percentage}%; background: ${this.getSignalColor(network.rssi)};"></div>
                    </div>
                </div>
            </div>
            <div class="detail-row">
                <span class="detail-label">Qualidade:</span>
                <span class="detail-value">${signalStrength.percentage}%</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Distância:</span>
                <span class="detail-value">${network.distance}m</span>
            </div>
        `;

        // Mostrar terminal
        this.modalTerminal.style.display = 'block';
        this.initializeTerminal();

        this.modal.classList.add('show');
    }

    getSignalColor(rssi) {
        if (rssi > -50) return '#00ff41';
        if (rssi > -70) return '#ffff00';
        if (rssi > -85) return '#ff6600';
        return '#ff0000';
    }

    closeModal() {
        this.modal.classList.remove('show');
        this.modalTerminal.style.display = 'none';
        this.currentNetwork = null;
    }

    showSimulationNotice() {
        // Criar notificação de simulação
        const noticeDiv = document.createElement('div');
        noticeDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #00ff41, #00cc33);
            color: #000;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 255, 65, 0.3);
            z-index: 10000;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
            font-weight: 600;
        `;
        noticeDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.2em;">🎯</span>
                <div>
                    <div>Modo Demonstração Ativo</div>
                    <div style="font-size: 0.8em; opacity: 0.8; margin-top: 2px;">Exibindo redes WiFi simuladas para visualização</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(noticeDiv);
        
        // Remover após 8 segundos
        setTimeout(() => {
            noticeDiv.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => noticeDiv.remove(), 300);
        }, 8000);
    }

    showError(message) {
        // Criar notificação de erro
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
            z-index: 10000;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
        `;
        errorDiv.textContent = message;

        document.body.appendChild(errorDiv);

        // Remover após 5 segundos
        setTimeout(() => {
            errorDiv.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => errorDiv.remove(), 300);
        }, 5000);
    }

    // Terminal Functions
    initializeTerminal() {
        this.terminalContent.innerHTML = `
            <div class="terminal-line">
                <span class="terminal-prompt">&gt;</span>
                <span class="terminal-text terminal-success">Conectado à rede: ${this.currentNetwork.ssid || 'Rede Oculta'}</span>
            </div>
            <div class="terminal-line">
                <span class="terminal-prompt">&gt;</span>
                <span class="terminal-text">Digite 'help' para ver os comandos disponíveis...</span>
            </div>
        `;
        this.terminalInput.focus();
    }

    handleTerminalInput(e) {
        if (e.key === 'Enter') {
            const command = this.terminalInput.value.trim();
            if (command) {
                this.executeTerminalCommand(command);
                this.terminalHistory.unshift(command);
                this.historyIndex = -1;
            }
            this.terminalInput.value = '';
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            if (this.historyIndex < this.terminalHistory.length - 1) {
                this.historyIndex++;
                this.terminalInput.value = this.terminalHistory[this.historyIndex];
            }
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            if (this.historyIndex > 0) {
                this.historyIndex--;
                this.terminalInput.value = this.terminalHistory[this.historyIndex];
            } else if (this.historyIndex === 0) {
                this.historyIndex = -1;
                this.terminalInput.value = '';
            }
        }
    }

    executeTerminalCommand(command) {
        const args = command.toLowerCase().split(' ');
        const cmd = args[0];

        // Adicionar comando ao terminal
        this.addTerminalLine(`> ${command}`, 'terminal-prompt');

        switch (cmd) {
            case 'help':
                this.showHelp();
                break;
            case 'ping':
                this.pingNetwork(args[1]);
                break;
            case 'traceroute':
                this.tracerouteNetwork();
                break;
            case 'nslookup':
                this.nslookupNetwork(args[1]);
                break;
            case 'connect':
                this.connectNetwork(args[1]);
                break;
            case 'disconnect':
                this.disconnectNetwork();
                break;
            case 'status':
                this.showNetworkStatus();
                break;
            case 'config':
                this.showNetworkConfig();
                break;
            case 'info':
                this.showNetworkInfo();
                break;
            case 'clear':
            case 'cls':
                this.clearTerminal();
                break;
            default:
                this.addTerminalLine(`Comando não reconhecido: ${cmd}`, 'terminal-error');
                this.addTerminalLine(`Digite 'help' para ver os comandos disponíveis.`, 'terminal-info');
        }

        // Scroll para o final
        this.terminalContent.scrollTop = this.terminalContent.scrollHeight;
    }

    addTerminalLine(text, className = '') {
        const line = document.createElement('div');
        line.className = 'terminal-line';
        line.innerHTML = `
            <span class="terminal-prompt">&gt;</span>
            <span class="terminal-text ${className}">${text}</span>
        `;
        this.terminalContent.appendChild(line);
    }

    showHelp() {
        const helpText = `
<span class="terminal-success">Comandos disponíveis para interação com a rede WiFi:</span>

<span class="terminal-info">ping [count]</span>        - Testa a latência com o Google DNS.
<span class="terminal-info">traceroute</span>         - Rastreia a rota até o Google DNS.
<span class="terminal-info">nslookup [host]</span>    - Resolve um nome de domínio.
<span class="terminal-info">connect &lt;senha&gt;</span>    - Conecta a esta rede (se necessário).
<span class="terminal-info">disconnect</span>        - Desconecta da rede WiFi atual.
<span class="terminal-info">status</span>            - Mostra o status da conexão WiFi.
<span class="terminal-info">config</span>            - Exibe a configuração de rede (ipconfig).
<span class="terminal-info">info</span>              - Mostra informações detalhadas desta rede.
<span class="terminal-info">clear / cls</span>       - Limpa o terminal.

<span class="terminal-warning">Nota:</span> Este é um terminal simulado para demonstração.
        `;
        this.addTerminalLine(helpText.trim());
    }

    pingNetwork(count = '4') {
        const pingCount = parseInt(count) || 4;
        this.addTerminalLine(`Executando ping para ******* com ${pingCount} pacotes...`, 'terminal-info');

        let currentPing = 0;
        const pingInterval = setInterval(() => {
            if (currentPing >= pingCount) {
                clearInterval(pingInterval);
                this.addTerminalLine(`\nEstatísticas do ping para *******:`, 'terminal-success');
                this.addTerminalLine(`    Pacotes: Enviados = ${pingCount}, Recebidos = ${pingCount}, Perdidos = 0 (0% de perda)`);
                this.addTerminalLine(`Aproximar um número de vezes em milissegundos:`);
                this.addTerminalLine(`    Mínimo = 12ms, Máximo = 45ms, Média = 28ms`);
                return;
            }

            currentPing++;
            const latency = Math.floor(Math.random() * 30) + 15; // 15-45ms
            const bytes = 32;
            this.addTerminalLine(`Resposta de *******: bytes=${bytes} tempo=${latency}ms TTL=117`);
        }, 1000);
    }

    tracerouteNetwork() {
        this.addTerminalLine(`Rastreando rota para dns.google [*******]`, 'terminal-info');
        this.addTerminalLine(`com no máximo 30 saltos:\n`);

        const hops = [
            { hop: 1, ip: '***********', name: 'gateway.local', time: '2ms' },
            { hop: 2, ip: '********', name: 'isp-router.net', time: '15ms' },
            { hop: 3, ip: '************', name: 'provider.com', time: '28ms' },
            { hop: 4, ip: '*******', name: 'dns.google', time: '35ms' }
        ];

        let currentHop = 0;
        const hopInterval = setInterval(() => {
            if (currentHop >= hops.length) {
                clearInterval(hopInterval);
                this.addTerminalLine(`\nRastreamento concluído.`, 'terminal-success');
                return;
            }

            const hop = hops[currentHop];
            this.addTerminalLine(`  ${hop.hop}    ${hop.time}    ${hop.time}    ${hop.time}  ${hop.ip} [${hop.name}]`);
            currentHop++;
        }, 800);
    }

    nslookupNetwork(host = 'google.com') {
        this.addTerminalLine(`Resolvendo ${host}...`, 'terminal-info');

        setTimeout(() => {
            this.addTerminalLine(`Servidor:  dns.google`);
            this.addTerminalLine(`Address:  *******\n`);
            this.addTerminalLine(`Resposta não autoritativa:`);
            this.addTerminalLine(`Nome:    ${host}`);
            this.addTerminalLine(`Address:  **************`);
            this.addTerminalLine(`Address:  2800:3f0:4004:c00::71`);
        }, 1500);
    }

    connectNetwork(password) {
        if (this.currentNetwork.security === 'Aberto') {
            this.addTerminalLine(`Conectando à rede aberta "${this.currentNetwork.ssid}"...`, 'terminal-info');
            setTimeout(() => {
                this.addTerminalLine(`Conectado com sucesso!`, 'terminal-success');
                this.addTerminalLine(`IP obtido: 192.168.1.${Math.floor(Math.random() * 200) + 10}`);
            }, 2000);
        } else if (password) {
            this.addTerminalLine(`Conectando à rede "${this.currentNetwork.ssid}" com senha...`, 'terminal-info');
            setTimeout(() => {
                const success = Math.random() > 0.3; // 70% chance de sucesso
                if (success) {
                    this.addTerminalLine(`Conectado com sucesso!`, 'terminal-success');
                    this.addTerminalLine(`IP obtido: 192.168.1.${Math.floor(Math.random() * 200) + 10}`);
                } else {
                    this.addTerminalLine(`Falha na autenticação. Senha incorreta.`, 'terminal-error');
                }
            }, 3000);
        } else {
            this.addTerminalLine(`Esta rede requer senha. Use: connect <senha>`, 'terminal-warning');
        }
    }

    disconnectNetwork() {
        this.addTerminalLine(`Desconectando da rede WiFi atual...`, 'terminal-info');
        setTimeout(() => {
            this.addTerminalLine(`Desconectado com sucesso.`, 'terminal-success');
        }, 1000);
    }

    showNetworkStatus() {
        this.addTerminalLine(`Status da conexão WiFi:`, 'terminal-info');
        this.addTerminalLine(`\nNome da rede: ${this.currentNetwork.ssid || 'Rede Oculta'}`);
        this.addTerminalLine(`BSSID: ${this.currentNetwork.bssid}`);
        this.addTerminalLine(`Força do sinal: ${this.currentNetwork.rssi} dBm`);
        this.addTerminalLine(`Canal: ${this.currentNetwork.channel}`);
        this.addTerminalLine(`Frequência: ${this.currentNetwork.frequencyBand}`);
        this.addTerminalLine(`Segurança: ${this.currentNetwork.security}`);
        this.addTerminalLine(`Distância estimada: ${this.currentNetwork.distance}m`);
        this.addTerminalLine(`Status: ${Math.random() > 0.5 ? 'Conectado' : 'Disponível'}`, 'terminal-success');
    }

    showNetworkConfig() {
        this.addTerminalLine(`Configuração de rede (ipconfig):`, 'terminal-info');
        this.addTerminalLine(`\nAdaptador de Rede sem Fio Wi-Fi:`);
        this.addTerminalLine(`   Sufixo DNS específico de conexão: local`);
        this.addTerminalLine(`   Endereço IPv4: 192.168.1.${Math.floor(Math.random() * 200) + 10}`);
        this.addTerminalLine(`   Máscara de Sub-rede: *************`);
        this.addTerminalLine(`   Gateway Padrão: ***********`);
        this.addTerminalLine(`   Servidor DNS: *******, *******`);
    }

    showNetworkInfo() {
        this.addTerminalLine(`Informações detalhadas da rede:`, 'terminal-info');
        this.addTerminalLine(`\n=== ${this.currentNetwork.ssid || 'Rede Oculta'} ===`);
        this.addTerminalLine(`BSSID: ${this.currentNetwork.bssid}`);
        this.addTerminalLine(`Força do sinal: ${this.currentNetwork.rssi} dBm`);
        this.addTerminalLine(`Qualidade: ${this.getSignalStrength(this.currentNetwork.rssi).percentage}%`);
        this.addTerminalLine(`Canal: ${this.currentNetwork.channel}`);
        this.addTerminalLine(`Banda de frequência: ${this.currentNetwork.frequencyBand}`);
        this.addTerminalLine(`Tipo de segurança: ${this.currentNetwork.security}`);
        this.addTerminalLine(`Distância estimada: ${this.currentNetwork.distance} metros`);
        this.addTerminalLine(`Fabricante estimado: ${this.getVendorFromMAC(this.currentNetwork.bssid)}`);
    }

    getVendorFromMAC(mac) {
        const vendors = {
            '00:1A': 'Cisco Systems',
            '00:2B': 'TP-Link',
            '00:3C': 'D-Link',
            '00:4D': 'Netgear',
            '00:5E': 'Linksys',
            '00:6F': 'ASUS',
            '00:7A': 'Huawei',
            '00:8B': 'Xiaomi',
            '00:9C': 'Samsung',
            '00:AD': 'Apple'
        };

        const prefix = mac.substring(0, 5);
        return vendors[prefix] || 'Fabricante desconhecido';
    }

    clearTerminal() {
        this.terminalContent.innerHTML = '';
        this.addTerminalLine(`Terminal limpo. Digite 'help' para ver os comandos.`, 'terminal-info');
    }
}

// Adicionar animações CSS dinamicamente
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Inicializar aplicação
const wifiRadar = new WiFiRadar();

// Expor globalmente para uso nos event handlers inline
window.wifiRadar = wifiRadar;
