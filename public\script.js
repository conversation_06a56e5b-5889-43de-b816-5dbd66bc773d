class WiFiRadar {
    constructor() {
        this.networks = [];
        this.ws = null;
        this.isScanning = false;
        this.radarRadius = 200; // Raio máximo do radar em pixels
        
        this.initializeElements();
        this.setupEventListeners();
        this.connectWebSocket();
    }

    initializeElements() {
        this.scanBtn = document.getElementById('scanBtn');
        this.status = document.getElementById('status');
        this.radarScreen = document.getElementById('radarScreen');
        this.networksList = document.getElementById('networksList');
        this.modal = document.getElementById('networkModal');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalBody = document.getElementById('modalBody');
        this.modalClose = document.getElementById('modalClose');
        this.wifiActions = document.getElementById('wifiActions');
        this.signalIndicator = document.getElementById('signalIndicator');
        this.signalStrengthText = document.getElementById('signalStrengthText');
        this.signalBars = document.getElementById('signalBars');
        this.outputContent = document.getElementById('outputContent');
        this.clearOutput = document.getElementById('clearOutput');

        // Action buttons
        this.pingSignalBtn = document.getElementById('pingSignalBtn');
        this.connectSignalBtn = document.getElementById('connectSignalBtn');
        this.analyzeSignalBtn = document.getElementById('analyzeSignalBtn');
        this.monitorSignalBtn = document.getElementById('monitorSignalBtn');
        this.speedTestBtn = document.getElementById('speedTestBtn');
        this.jamSignalBtn = document.getElementById('jamSignalBtn');

        this.currentNetwork = null;
        this.terminalHistory = [];
        this.historyIndex = -1;
    }

    setupEventListeners() {
        this.scanBtn.addEventListener('click', () => this.scanNetworks());
        this.modalClose.addEventListener('click', () => this.closeModal());
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) this.closeModal();
        });

        // WiFi Actions event listeners
        this.pingSignalBtn.addEventListener('click', () => this.pingSignal());
        this.connectSignalBtn.addEventListener('click', () => this.connectToSignal());
        this.analyzeSignalBtn.addEventListener('click', () => this.analyzeSignal());
        this.monitorSignalBtn.addEventListener('click', () => this.monitorSignal());
        this.speedTestBtn.addEventListener('click', () => this.speedTestSignal());
        this.jamSignalBtn.addEventListener('click', () => this.jamSignal());
        this.clearOutput.addEventListener('click', () => this.clearActionOutput());

        // Fechar modal com ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.closeModal();
            }
        });
    }

    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;
        
        try {
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = () => {
                console.log('WebSocket conectado');
                this.updateStatus('Conectado', 'connected');
            };
            
            this.ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'networks') {
                    this.updateNetworks(data.networks, data.simulated);
                }
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket desconectado');
                this.updateStatus('Desconectado', '');
                // Tentar reconectar após 3 segundos
                setTimeout(() => this.connectWebSocket(), 3000);
            };
            
            this.ws.onerror = (error) => {
                console.error('Erro WebSocket:', error);
                this.updateStatus('Erro de conexão', '');
            };
        } catch (error) {
            console.error('Erro ao conectar WebSocket:', error);
            this.updateStatus('Erro de conexão', '');
        }
    }

    updateStatus(message, className = '') {
        this.status.textContent = message;
        this.status.className = `status ${className}`;
    }

    async scanNetworks() {
        if (this.isScanning) return;
        
        this.isScanning = true;
        this.scanBtn.classList.add('scanning');
        this.scanBtn.innerHTML = '<span class="btn-icon">🔄</span>Escaneando...';
        this.updateStatus('Escaneando redes...', 'scanning');
        
        try {
            // Enviar comando de scan via WebSocket se conectado
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({ type: 'scan' }));
            } else {
                // Fallback para API REST
                const response = await fetch('/api/scan');
                const data = await response.json();
                
                if (data.success) {
                    this.updateNetworks(data.networks, data.simulated);
                } else {
                    throw new Error(data.error || 'Erro ao escanear redes');
                }
            }
        } catch (error) {
            console.error('Erro no escaneamento:', error);
            this.updateStatus('Erro no escaneamento', '');
            this.showError('Erro ao escanear redes WiFi: ' + error.message);
        } finally {
            setTimeout(() => {
                this.isScanning = false;
                this.scanBtn.classList.remove('scanning');
                this.scanBtn.innerHTML = '<span class="btn-icon">🔄</span>Escanear';
            }, 1000);
        }
    }

    updateNetworks(networks, isSimulated = false) {
        this.networks = networks;
        this.renderRadar();
        this.renderNetworksList();
        
        const statusMessage = isSimulated 
            ? `${networks.length} redes simuladas (demonstração)`
            : `${networks.length} redes encontradas`;
        
        this.updateStatus(statusMessage, 'connected');
        
        // Mostrar notificação se estiver usando dados simulados
        if (isSimulated && networks.length > 0) {
            this.showSimulationNotice();
        }
    }

    renderRadar() {
        // Remover pontos existentes
        const existingPoints = this.radarScreen.querySelectorAll('.router-point');
        existingPoints.forEach(point => point.remove());

        this.networks.forEach((network, index) => {
            const point = this.createRouterPoint(network, index);
            this.radarScreen.appendChild(point);
        });
    }

    createRouterPoint(network, index) {
        const point = document.createElement('div');
        point.className = `router-point ${this.getSignalClass(network.rssi)}`;

        // Calcular posição baseada na distância (RSSI) e direção
        const distance = this.calculateRadarDistance(network.rssi);

        let angle;
        if (network.direction) {
            // Usar direção real se disponível
            angle = this.getAngleFromDirection(network.direction);
        } else {
            // Fallback: distribuir uniformemente em círculos baseados na distância
            const pointsAtSameDistance = this.networks.filter(n =>
                Math.abs(this.calculateRadarDistance(n.rssi) - distance) < 10
            );
            const indexAtDistance = pointsAtSameDistance.findIndex(n => n.bssid === network.bssid);
            const totalAtDistance = pointsAtSameDistance.length;
            angle = (indexAtDistance * 360 / Math.max(totalAtDistance, 8)) * (Math.PI / 180);
        }

        const x = 250 + distance * Math.cos(angle);
        const y = 250 + distance * Math.sin(angle);

        point.style.left = `${x - 5}px`;
        point.style.top = `${y - 5}px`;

        // Adicionar label com SSID, distância e direção
        const label = document.createElement('div');
        label.className = 'router-label';
        label.innerHTML = `
            <div class="label-ssid">${network.ssid || 'Rede Oculta'}</div>
            <div class="label-distance">${network.distance}m ${network.direction ? `(${network.direction})` : ''}</div>
        `;
        point.appendChild(label);

        // Adicionar evento de clique
        point.addEventListener('click', () => this.showNetworkDetails(network));

        return point;
    }

    getAngleFromDirection(direction) {
        // Converter direção para ângulo em radianos
        const directionMap = {
            'N': 270,   // Norte (topo)
            'NE': 315,  // Nordeste
            'E': 0,     // Leste (direita)
            'SE': 45,   // Sudeste
            'S': 90,    // Sul (baixo)
            'SW': 135,  // Sudoeste
            'W': 180,   // Oeste (esquerda)
            'NW': 225   // Noroeste
        };

        const degrees = directionMap[direction] || 0;
        return degrees * (Math.PI / 180);
    }

    calculateRadarDistance(rssi) {
        // Mapear RSSI para distância no radar (0 a radarRadius)
        // RSSI típico: -30 (muito forte) a -90 (muito fraco)
        const minRssi = -90;
        const maxRssi = -30;
        
        // Normalizar RSSI para 0-1
        const normalizedRssi = Math.max(0, Math.min(1, (rssi - minRssi) / (maxRssi - minRssi)));
        
        // Inverter (sinal mais forte = mais próximo do centro)
        const distance = (1 - normalizedRssi) * this.radarRadius;
        
        return Math.max(20, distance); // Mínimo de 20px do centro
    }

    getSignalClass(rssi) {
        if (rssi > -50) return 'strong';
        if (rssi > -70) return 'medium';
        if (rssi > -85) return 'weak';
        return 'very-weak';
    }

    getSignalStrength(rssi) {
        if (rssi > -50) return { text: 'Excelente', percentage: 100 };
        if (rssi > -60) return { text: 'Muito Bom', percentage: 80 };
        if (rssi > -70) return { text: 'Bom', percentage: 60 };
        if (rssi > -80) return { text: 'Fraco', percentage: 40 };
        return { text: 'Muito Fraco', percentage: 20 };
    }

    renderNetworksList() {
        if (this.networks.length === 0) {
            this.networksList.innerHTML = `
                <div class="no-networks">
                    <p>Nenhuma rede WiFi encontrada</p>
                </div>
            `;
            return;
        }

        // Ordenar por força do sinal
        const sortedNetworks = [...this.networks].sort((a, b) => b.rssi - a.rssi);

        this.networksList.innerHTML = sortedNetworks.map(network => `
            <div class="network-item" onclick="wifiRadar.showNetworkDetails(${JSON.stringify(network).replace(/"/g, '&quot;')})">
                <div class="network-name">${network.ssid || 'Rede Oculta'}</div>
                <div class="network-details">
                    <span>${network.rssi} dBm</span>
                    <span>${network.frequencyBand}</span>
                    <span>${network.security}</span>
                </div>
            </div>
        `).join('');
    }

    showNetworkDetails(network) {
        const signalStrength = this.getSignalStrength(network.rssi);
        this.currentNetwork = network;

        this.modalTitle.textContent = network.ssid || 'Rede Oculta';

        this.modalBody.innerHTML = `
            <div class="detail-row">
                <span class="detail-label">SSID:</span>
                <span class="detail-value">${network.ssid || 'Oculto'}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">BSSID:</span>
                <span class="detail-value">${network.bssid}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Força do Sinal:</span>
                <div class="detail-value signal-strength">
                    <span>${network.rssi} dBm (${signalStrength.text})</span>
                    <div class="signal-bar">
                        <div class="signal-fill ${this.getSignalClass(network.rssi)}"
                             style="width: ${signalStrength.percentage}%; background: ${this.getSignalColor(network.rssi)};"></div>
                    </div>
                </div>
            </div>
            <div class="detail-row">
                <span class="detail-label">Qualidade:</span>
                <span class="detail-value">${signalStrength.percentage}%</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Distância:</span>
                <span class="detail-value">${network.distance}m</span>
            </div>
        `;

        // Mostrar ações WiFi
        this.wifiActions.style.display = 'block';
        this.initializeWiFiActions();

        this.modal.classList.add('show');
    }

    getSignalColor(rssi) {
        if (rssi > -50) return '#00ff41';
        if (rssi > -70) return '#ffff00';
        if (rssi > -85) return '#ff6600';
        return '#ff0000';
    }

    closeModal() {
        this.modal.classList.remove('show');
        this.wifiActions.style.display = 'none';
        this.currentNetwork = null;
    }

    showSimulationNotice() {
        // Criar notificação de simulação
        const noticeDiv = document.createElement('div');
        noticeDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #00ff41, #00cc33);
            color: #000;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 255, 65, 0.3);
            z-index: 10000;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
            font-weight: 600;
        `;
        noticeDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.2em;">🎯</span>
                <div>
                    <div>Modo Demonstração Ativo</div>
                    <div style="font-size: 0.8em; opacity: 0.8; margin-top: 2px;">Exibindo redes WiFi simuladas para visualização</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(noticeDiv);
        
        // Remover após 8 segundos
        setTimeout(() => {
            noticeDiv.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => noticeDiv.remove(), 300);
        }, 8000);
    }

    showError(message) {
        // Criar notificação de erro
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
            z-index: 10000;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
        `;
        errorDiv.textContent = message;

        document.body.appendChild(errorDiv);

        // Remover após 5 segundos
        setTimeout(() => {
            errorDiv.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => errorDiv.remove(), 300);
        }, 5000);
    }

    // WiFi Actions Functions
    initializeWiFiActions() {
        this.updateSignalIndicator();
        this.addOutputLine(`🔗 Conectado à rede: ${this.currentNetwork.ssid || 'Rede Oculta'}`, 'output-success');
        this.addOutputLine(`📡 Selecione uma ação para interagir com o sinal...`, 'output-info');
    }

    updateSignalIndicator() {
        const rssi = this.currentNetwork.rssi;
        const signalStrength = this.getSignalStrength(rssi);

        this.signalStrengthText.textContent = `Sinal: ${rssi} dBm`;

        // Atualizar barras de sinal
        const bars = this.signalBars.querySelectorAll('.signal-bar');
        const activeBars = Math.ceil((signalStrength.percentage / 100) * 4);

        bars.forEach((bar, index) => {
            if (index < activeBars) {
                bar.classList.add('active');
            } else {
                bar.classList.remove('active');
            }
        });
    }

    addOutputLine(text, className = '') {
        const line = document.createElement('div');
        line.className = `output-line ${className}`;
        line.innerHTML = text;
        this.outputContent.appendChild(line);
        this.outputContent.scrollTop = this.outputContent.scrollHeight;
    }

    clearActionOutput() {
        this.outputContent.innerHTML = '';
        this.addOutputLine(`📋 Output limpo. Selecione uma ação...`, 'output-info');
    }

    pingSignal() {
        this.addOutputLine(`🔄 Iniciando ping direto para ${this.currentNetwork.ssid}...`, 'output-info');
        this.addOutputLine(`📡 BSSID: ${this.currentNetwork.bssid}`);

        let pingCount = 0;
        const maxPings = 4;

        const pingInterval = setInterval(() => {
            if (pingCount >= maxPings) {
                clearInterval(pingInterval);
                this.addOutputLine(`✅ Ping concluído. Sinal estável.`, 'output-success');
                return;
            }

            pingCount++;
            // Simular variação realista baseada na distância e RSSI
            const baseLatency = Math.max(1, this.currentNetwork.distance * 0.1);
            const variation = (Math.random() - 0.5) * 10;
            const latency = Math.max(0.1, baseLatency + variation).toFixed(1);

            this.addOutputLine(`📶 Ping ${pingCount}: ${latency}ms - RSSI: ${this.currentNetwork.rssi}dBm`);
        }, 800);
    }

    connectToSignal() {
        this.addOutputLine(`🔗 Tentando conectar à rede ${this.currentNetwork.ssid}...`, 'output-info');

        if (this.currentNetwork.security === 'Aberto') {
            setTimeout(() => {
                this.addOutputLine(`✅ Conectado com sucesso à rede aberta!`, 'output-success');
                this.addOutputLine(`🌐 IP obtido: 192.168.1.${Math.floor(Math.random() * 200) + 10}`);
                this.addOutputLine(`📊 Velocidade estimada: ${Math.floor(Math.random() * 100) + 50} Mbps`);
            }, 2000);
        } else {
            const password = prompt(`Digite a senha para ${this.currentNetwork.ssid}:`);
            if (password) {
                setTimeout(() => {
                    const success = Math.random() > 0.2; // 80% chance de sucesso
                    if (success) {
                        this.addOutputLine(`✅ Autenticação bem-sucedida!`, 'output-success');
                        this.addOutputLine(`🌐 IP obtido: 192.168.1.${Math.floor(Math.random() * 200) + 10}`);
                        this.addOutputLine(`📊 Velocidade estimada: ${Math.floor(Math.random() * 100) + 50} Mbps`);
                    } else {
                        this.addOutputLine(`❌ Falha na autenticação. Senha incorreta.`, 'output-error');
                    }
                }, 3000);
            } else {
                this.addOutputLine(`⚠️ Conexão cancelada pelo usuário.`, 'output-warning');
            }
        }
    }

    analyzeSignal() {
        this.addOutputLine(`🔍 Analisando sinal de ${this.currentNetwork.ssid}...`, 'output-info');

        setTimeout(() => {
            const analysis = this.performSignalAnalysis();
            this.addOutputLine(`📊 === ANÁLISE COMPLETA ===`, 'output-success');
            this.addOutputLine(`🏷️ SSID: ${this.currentNetwork.ssid || 'Oculto'}`);
            this.addOutputLine(`📍 BSSID: ${this.currentNetwork.bssid}`);
            this.addOutputLine(`📶 Força: ${this.currentNetwork.rssi} dBm (${analysis.signalQuality})`);
            this.addOutputLine(`📏 Distância: ${this.currentNetwork.distance}m`);
            this.addOutputLine(`🧭 Direção: ${this.currentNetwork.direction || 'Não detectada'}`);
            this.addOutputLine(`📻 Canal: ${this.currentNetwork.channel} (${this.currentNetwork.frequencyBand})`);
            this.addOutputLine(`🔒 Segurança: ${this.currentNetwork.security}`);
            this.addOutputLine(`🏭 Fabricante: ${analysis.vendor}`);
            this.addOutputLine(`⚡ Interferência: ${analysis.interference}`);
            this.addOutputLine(`📈 Estabilidade: ${analysis.stability}`);
            this.addOutputLine(`🎯 Recomendação: ${analysis.recommendation}`, 'output-warning');
        }, 2500);
    }

    monitorSignal() {
        this.addOutputLine(`👁️ Iniciando monitoramento contínuo de ${this.currentNetwork.ssid}...`, 'output-info');
        this.addOutputLine(`⏱️ Monitoramento por 30 segundos...`);

        let monitorCount = 0;
        const maxMonitor = 10;

        const monitorInterval = setInterval(() => {
            if (monitorCount >= maxMonitor) {
                clearInterval(monitorInterval);
                this.addOutputLine(`✅ Monitoramento concluído.`, 'output-success');
                return;
            }

            monitorCount++;
            // Simular variações no sinal
            const rssiVariation = Math.floor((Math.random() - 0.5) * 10);
            const currentRssi = this.currentNetwork.rssi + rssiVariation;
            const timestamp = new Date().toLocaleTimeString();

            this.addOutputLine(`📊 ${timestamp} - RSSI: ${currentRssi}dBm | Canal: ${this.currentNetwork.channel} | Qualidade: ${this.getSignalStrength(currentRssi).percentage}%`);
        }, 3000);
    }

    speedTestSignal() {
        this.addOutputLine(`⚡ Iniciando teste de velocidade para ${this.currentNetwork.ssid}...`, 'output-info');

        // Simular teste de download
        this.addOutputLine(`📥 Testando download...`);
        let progress = 0;
        const downloadInterval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress >= 100) {
                clearInterval(downloadInterval);
                const downloadSpeed = Math.floor(Math.random() * 100) + 20;
                this.addOutputLine(`📥 Download: ${downloadSpeed} Mbps`, 'output-success');

                // Simular teste de upload
                this.addOutputLine(`📤 Testando upload...`);
                setTimeout(() => {
                    const uploadSpeed = Math.floor(downloadSpeed * 0.3) + Math.floor(Math.random() * 20);
                    this.addOutputLine(`📤 Upload: ${uploadSpeed} Mbps`, 'output-success');

                    // Ping
                    const ping = Math.floor(Math.random() * 50) + 10;
                    this.addOutputLine(`🏓 Ping: ${ping}ms`, 'output-success');
                    this.addOutputLine(`✅ Teste de velocidade concluído!`, 'output-success');
                }, 2000);
                return;
            }
            this.addOutputLine(`📊 Progresso: ${Math.floor(progress)}%`);
        }, 1000);
    }

    jamSignal() {
        this.addOutputLine(`🚫 ATENÇÃO: Teste de interferência iniciado...`, 'output-warning');
        this.addOutputLine(`⚠️ Esta ação é apenas para fins educacionais!`, 'output-warning');

        setTimeout(() => {
            this.addOutputLine(`📡 Analisando vulnerabilidades do sinal...`);
            this.addOutputLine(`🔍 Verificando canais adjacentes...`);
            this.addOutputLine(`📊 Canal ${this.currentNetwork.channel}: ${Math.random() > 0.5 ? 'Congestionado' : 'Livre'}`);
            this.addOutputLine(`🛡️ Proteção WPS: ${Math.random() > 0.7 ? 'Ativada' : 'Desativada'}`);
            this.addOutputLine(`🔐 Força da criptografia: ${this.currentNetwork.security === 'WPA3' ? 'Alta' : this.currentNetwork.security === 'WPA2' ? 'Média' : 'Baixa'}`);
            this.addOutputLine(`✅ Análise de segurança concluída.`, 'output-info');
        }, 3000);
    }

    performSignalAnalysis() {
        const rssi = this.currentNetwork.rssi;
        const signalStrength = this.getSignalStrength(rssi);

        // Determinar qualidade do sinal
        let signalQuality;
        if (rssi > -50) signalQuality = 'Excelente';
        else if (rssi > -60) signalQuality = 'Muito Boa';
        else if (rssi > -70) signalQuality = 'Boa';
        else if (rssi > -80) signalQuality = 'Fraca';
        else signalQuality = 'Muito Fraca';

        // Determinar fabricante baseado no BSSID
        const vendor = this.getVendorFromMAC(this.currentNetwork.bssid);

        // Simular análise de interferência
        const interferenceLevel = Math.random();
        let interference;
        if (interferenceLevel > 0.8) interference = 'Alta';
        else if (interferenceLevel > 0.5) interference = 'Média';
        else interference = 'Baixa';

        // Simular estabilidade
        const stabilityLevel = Math.random();
        let stability;
        if (stabilityLevel > 0.7) stability = 'Estável';
        else if (stabilityLevel > 0.4) stability = 'Moderada';
        else stability = 'Instável';

        // Gerar recomendação
        let recommendation;
        if (rssi > -60 && interference === 'Baixa') {
            recommendation = 'Ótima para conexão';
        } else if (rssi > -70) {
            recommendation = 'Adequada para uso geral';
        } else if (rssi > -80) {
            recommendation = 'Adequada apenas para tarefas leves';
        } else {
            recommendation = 'Não recomendada para uso';
        }

        return {
            signalQuality,
            vendor,
            interference,
            stability,
            recommendation
        };
    }

    // Função auxiliar mantida para compatibilidade

    getVendorFromMAC(mac) {
        const vendors = {
            '00:1A': 'Cisco Systems',
            '00:2B': 'TP-Link',
            '00:3C': 'D-Link',
            '00:4D': 'Netgear',
            '00:5E': 'Linksys',
            '00:6F': 'ASUS',
            '00:7A': 'Huawei',
            '00:8B': 'Xiaomi',
            '00:9C': 'Samsung',
            '00:AD': 'Apple'
        };

        const prefix = mac.substring(0, 5);
        return vendors[prefix] || 'Fabricante desconhecido';
    }

    // Função removida - substituída por clearActionOutput
}

// Adicionar animações CSS dinamicamente
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Inicializar aplicação
const wifiRadar = new WiFiRadar();

// Expor globalmente para uso nos event handlers inline
window.wifiRadar = wifiRadar;
