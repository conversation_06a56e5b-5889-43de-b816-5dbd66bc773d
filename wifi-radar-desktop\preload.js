const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expor APIs seguras para o renderer
contextBridge.exposeInMainWorld('electronAPI', {
    // Escaneamento WiFi
    scanWifi: () => ipcRenderer.invoke('scan-wifi'),
    
    // Conexão WiFi
    connectWifi: (ssid, password) => ipcRenderer.invoke('connect-wifi', ssid, password),
    disconnectWifi: () => ipcRenderer.invoke('disconnect-wifi'),
    
    // Comandos de rede
    pingHost: (host, count) => ipcRenderer.invoke('ping-host', host, count),
    tracerouteHost: (host) => ipcRenderer.invoke('traceroute-host', host),
    nslookupHost: (host) => ipcRenderer.invoke('nslookup-host', host),
    getNetworkInfo: () => ipcRenderer.invoke('get-network-info'),
    getWifiStatus: () => ipcRenderer.invoke('get-wifi-status'),
    
    // Listeners para eventos automáticos
    onAutoScanTrigger: (callback) => ipcRenderer.on('auto-scan-trigger', callback),
    removeAutoScanListener: () => ipcRenderer.removeAllListeners('auto-scan-trigger'),
    
    // Diálogos
    showErrorDialog: (title, content) => ipcRenderer.invoke('show-error-dialog', title, content),
    showInfoDialog: (title, content) => ipcRenderer.invoke('show-info-dialog', title, content),
    
    // Informações do sistema
    platform: process.platform,
    version: process.versions.electron
});
