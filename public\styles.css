/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #00ff41;
}

.title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    color: #00ff41;
    text-shadow: 0 0 20px #00ff41;
    display: flex;
    align-items: center;
    gap: 15px;
}

.radar-icon {
    font-size: 3rem;
    animation: rotate 4s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.scan-btn {
    background: linear-gradient(45deg, #00ff41, #00cc33);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    color: #000;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 255, 65, 0.3);
}

.scan-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 255, 65, 0.4);
}

.scan-btn:active {
    transform: translateY(0);
}

.scan-btn.scanning {
    background: linear-gradient(45deg, #ff6600, #ff4400);
    box-shadow: 0 4px 15px rgba(255, 102, 0, 0.3);
}

.scan-btn.scanning .btn-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #cccccc;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #666;
    animation: pulse 2s infinite;
}

.status.connected .status-dot {
    background: #00ff41;
}

.status.scanning .status-dot {
    background: #ff6600;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Main Content */
.main-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 30px;
    align-items: start;
}

/* Radar */
.radar-container {
    position: relative;
}

.radar-screen {
    width: 500px;
    height: 500px;
    position: relative;
    background: radial-gradient(circle, rgba(0, 255, 65, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);
    border: 2px solid #00ff41;
    border-radius: 50%;
    margin: 0 auto;
    box-shadow: 
        0 0 50px rgba(0, 255, 65, 0.3),
        inset 0 0 50px rgba(0, 255, 65, 0.1);
}

.radar-circle {
    position: absolute;
    border: 1px solid rgba(0, 255, 65, 0.3);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.radar-line {
    position: absolute;
    background: rgba(0, 255, 65, 0.3);
}

.radar-line-h {
    width: 100%;
    height: 1px;
    top: 50%;
    left: 0;
}

.radar-line-v {
    width: 1px;
    height: 100%;
    left: 50%;
    top: 0;
}

.radar-line-d1 {
    width: 1px;
    height: 141.42%;
    left: 50%;
    top: -20.71%;
    transform: rotate(45deg);
}

.radar-line-d2 {
    width: 1px;
    height: 141.42%;
    left: 50%;
    top: -20.71%;
    transform: rotate(-45deg);
}

.radar-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.user-dot {
    width: 12px;
    height: 12px;
    background: #00ff41;
    border-radius: 50%;
    margin: 0 auto 5px;
    box-shadow: 0 0 15px #00ff41;
    animation: pulse 2s infinite;
}

.user-label {
    font-size: 0.8rem;
    color: #00ff41;
    font-weight: 500;
}

.radar-sweep {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 250px;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #00ff41 100%);
    transform-origin: 0 50%;
    animation: sweep 4s linear infinite;
    opacity: 0.7;
}

@keyframes sweep {
    from { transform: translate(0, -50%) rotate(0deg); }
    to { transform: translate(0, -50%) rotate(360deg); }
}

/* Pontos dos roteadores */
.router-point {
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    animation: fadeIn 0.5s ease-in;
}

.router-point:hover {
    transform: scale(1.5);
    box-shadow: 0 0 20px currentColor;
}

.router-point.strong {
    background: #00ff41;
    box-shadow: 0 0 10px #00ff41;
}

.router-point.medium {
    background: #ffff00;
    box-shadow: 0 0 10px #ffff00;
}

.router-point.weak {
    background: #ff6600;
    box-shadow: 0 0 10px #ff6600;
}

.router-point.very-weak {
    background: #ff0000;
    box-shadow: 0 0 10px #ff0000;
}

.router-label {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    white-space: nowrap;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.router-point:hover .router-label {
    opacity: 1;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0); }
    to { opacity: 1; transform: scale(1); }
}

/* Legenda */
.radar-legend {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.8rem;
    color: #cccccc;
}

.legend-circle {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    box-shadow: 0 0 5px currentColor;
}

/* Painel de informações */
.info-panel {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 65, 0.3);
    border-radius: 10px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.info-panel h3 {
    color: #00ff41;
    margin-bottom: 15px;
    font-family: 'Orbitron', monospace;
}

.networks-list {
    max-height: 400px;
    overflow-y: auto;
}

.network-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.network-item:hover {
    background: rgba(0, 255, 65, 0.1);
    border-color: #00ff41;
    transform: translateX(5px);
}

.network-name {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 5px;
}

.network-details {
    font-size: 0.8rem;
    color: #cccccc;
    display: flex;
    justify-content: space-between;
}

.no-networks {
    text-align: center;
    color: #666;
    padding: 40px 20px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid #00ff41;
    border-radius: 15px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 255, 65, 0.3);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: scale(0.8) translateY(-50px); opacity: 0; }
    to { transform: scale(1) translateY(0); opacity: 1; }
}

.modal-header {
    background: rgba(0, 255, 65, 0.1);
    padding: 20px;
    border-bottom: 1px solid rgba(0, 255, 65, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: #00ff41;
    font-family: 'Orbitron', monospace;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ff6600;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: #00ff41;
    min-width: 120px;
}

.detail-value {
    color: #ffffff;
    text-align: right;
    flex: 1;
}

.signal-strength {
    display: flex;
    align-items: center;
    gap: 10px;
}

.signal-bar {
    width: 100px;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.signal-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Responsividade */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .radar-screen {
        width: 400px;
        height: 400px;
    }
    
    .info-panel {
        order: -1;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .radar-screen {
        width: 300px;
        height: 300px;
    }
    
    .radar-legend {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #00ff41;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #00cc33;
}
