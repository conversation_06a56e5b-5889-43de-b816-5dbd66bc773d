/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
    user-select: none;
}

.container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #00ff41;
}

.title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    color: #00ff41;
    text-shadow: 0 0 20px #00ff41;
    display: flex;
    align-items: center;
    gap: 15px;
}

.radar-icon {
    font-size: 3rem;
    animation: rotate 4s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.scan-btn {
    background: linear-gradient(45deg, #00ff41, #00cc33);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    color: #000;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 255, 65, 0.3);
    font-family: 'Orbitron', monospace;
}

.scan-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 255, 65, 0.4);
    background: linear-gradient(45deg, #00cc33, #00ff41);
}

.scan-btn:active {
    transform: translateY(0);
}

.auto-scan-btn {
    background: linear-gradient(45deg, #4169E1, #6495ED);
    color: #fff;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Orbitron', monospace;
    box-shadow: 0 4px 15px rgba(65, 105, 225, 0.3);
    margin-left: 10px;
}

.auto-scan-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(65, 105, 225, 0.4);
}

.auto-scan-btn.active {
    background: linear-gradient(45deg, #00ff41, #00cc33);
    color: #000;
    box-shadow: 0 4px 15px rgba(0, 255, 65, 0.3);
}

.auto-scan-btn.inactive {
    background: linear-gradient(45deg, #ff4444, #cc0000);
    color: #fff;
    box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
}

.scan-btn.scanning {
    background: linear-gradient(45deg, #ff6600, #ff4400);
    box-shadow: 0 4px 15px rgba(255, 102, 0, 0.3);
}

.scan-btn.scanning .btn-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #cccccc;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #666;
    animation: pulse 2s infinite;
}

.status.connected .status-dot {
    background: #00ff41;
}

.status.scanning .status-dot {
    background: #ff6600;
}

.status.error .status-dot {
    background: #ff4444;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.app-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.desktop-badge, .real-data-badge {
    font-size: 0.7rem;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 600;
    text-align: center;
}

.desktop-badge {
    background: linear-gradient(45deg, #4169E1, #6495ED);
    color: white;
}

.real-data-badge {
    background: linear-gradient(45deg, #32CD32, #228B22);
    color: white;
}

/* Main Content */
.main-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
    align-items: start;
}

/* Radar */
.radar-container {
    position: relative;
}

.radar-screen {
    width: 600px;
    height: 600px;
    position: relative;
    background: radial-gradient(circle, rgba(0, 255, 65, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);
    border: 2px solid #00ff41;
    border-radius: 50%;
    margin: 0 auto;
    box-shadow: 
        0 0 50px rgba(0, 255, 65, 0.3),
        inset 0 0 50px rgba(0, 255, 65, 0.1);
}

.radar-circle {
    position: absolute;
    border: 1px solid rgba(0, 255, 65, 0.3);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.radar-line {
    position: absolute;
    background: rgba(0, 255, 65, 0.3);
}

.radar-line-h {
    width: 100%;
    height: 1px;
    top: 50%;
    left: 0;
}

.radar-line-v {
    width: 1px;
    height: 100%;
    left: 50%;
    top: 0;
}

.radar-line-d1 {
    width: 1px;
    height: 141.42%;
    left: 50%;
    top: -20.71%;
    transform: rotate(45deg);
}

.radar-line-d2 {
    width: 1px;
    height: 141.42%;
    left: 50%;
    top: -20.71%;
    transform: rotate(-45deg);
}

.radar-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.user-dot {
    width: 14px;
    height: 14px;
    background: #00ff41;
    border-radius: 50%;
    margin: 0 auto 5px;
    box-shadow: 0 0 15px #00ff41;
    animation: pulse 2s infinite;
}

.user-label {
    font-size: 0.8rem;
    color: #00ff41;
    font-weight: 600;
}

.radar-sweep {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #00ff41 100%);
    transform-origin: 0 50%;
    animation: sweep 4s linear infinite;
    opacity: 0.7;
}

@keyframes sweep {
    from { transform: translate(0, -50%) rotate(0deg); }
    to { transform: translate(0, -50%) rotate(360deg); }
}

/* Pontos dos roteadores */
.router-point {
    position: absolute;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    animation: fadeIn 0.5s ease-in;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.router-point:hover {
    transform: scale(1.8);
    box-shadow: 0 0 25px currentColor;
    border-color: white;
}

.router-point.strong {
    background: #00ff41;
    box-shadow: 0 0 12px #00ff41;
}

.router-point.medium {
    background: #ffff00;
    box-shadow: 0 0 12px #ffff00;
}

.router-point.weak {
    background: #ff6600;
    box-shadow: 0 0 12px #ff6600;
}

.router-point.very-weak {
    background: #ff0000;
    box-shadow: 0 0 12px #ff0000;
}

.router-label {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.7rem;
    white-space: nowrap;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    border: 1px solid rgba(0, 255, 65, 0.5);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.router-point:hover .router-label {
    opacity: 1;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0); }
    to { opacity: 1; transform: scale(1); }
}

/* Legenda e informações do radar */
.radar-legend {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    gap: 25px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.8rem;
    color: #cccccc;
    padding: 5px 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(0, 255, 65, 0.2);
}

.legend-circle {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    box-shadow: 0 0 5px currentColor;
}

.radar-info {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 30px;
    font-size: 0.8rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
}

.info-label {
    color: #888;
}

.info-value {
    color: #00ff41;
    font-weight: 600;
}

.auto-scan-active {
    animation: pulse 2s infinite;
}

/* Painel de informações */
.info-panel {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 65, 0.3);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    height: fit-content;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 255, 65, 0.2);
}

.panel-header h3 {
    color: #00ff41;
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
}

.network-count {
    background: rgba(0, 255, 65, 0.2);
    color: #00ff41;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.networks-list {
    max-height: 500px;
    overflow-y: auto;
}

.network-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.network-item:hover {
    background: rgba(0, 255, 65, 0.1);
    border-color: #00ff41;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 255, 65, 0.2);
}

.network-name {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8px;
    font-size: 1rem;
}

.network-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 0.8rem;
    color: #cccccc;
}

.network-detail-item {
    display: flex;
    justify-content: space-between;
}

.network-signal {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 20px;
    height: 15px;
    display: flex;
    align-items: end;
    gap: 2px;
}

.signal-bar {
    width: 3px;
    background: #333;
    border-radius: 1px;
}

.signal-bar.active {
    background: #00ff41;
}

.signal-bar:nth-child(1) { height: 20%; }
.signal-bar:nth-child(2) { height: 40%; }
.signal-bar:nth-child(3) { height: 60%; }
.signal-bar:nth-child(4) { height: 80%; }
.signal-bar:nth-child(5) { height: 100%; }

.no-networks {
    text-align: center;
    color: #666;
    padding: 60px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(0, 255, 65, 0.3);
    border-top: 3px solid #00ff41;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid #00ff41;
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 255, 65, 0.3);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: scale(0.8) translateY(-50px); opacity: 0; }
    to { transform: scale(1) translateY(0); opacity: 1; }
}

.modal-header {
    background: rgba(0, 255, 65, 0.1);
    padding: 25px;
    border-bottom: 1px solid rgba(0, 255, 65, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: #00ff41;
    font-family: 'Orbitron', monospace;
    margin: 0;
    font-size: 1.5rem;
}

.modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ff6600;
}

.modal-body {
    padding: 25px;
    max-height: 50vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: flex-end;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #00ff41;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: #00ff41;
    min-width: 140px;
}

.detail-value {
    color: #ffffff;
    text-align: right;
    flex: 1;
}

.signal-strength {
    display: flex;
    align-items: center;
    gap: 15px;
}

.signal-bar-container {
    width: 120px;
    height: 10px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    overflow: hidden;
}

.signal-fill {
    height: 100%;
    border-radius: 5px;
    transition: width 0.3s ease;
}

/* Notificação de erro */
.error-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #ff4444, #cc0000);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(255, 68, 68, 0.4);
    z-index: 10000;
    max-width: 400px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.error-notification.show {
    transform: translateX(0);
}

.error-content {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    gap: 15px;
}

.error-icon {
    font-size: 1.5rem;
}

.error-text {
    flex: 1;
}

.error-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.error-message {
    font-size: 0.9rem;
    opacity: 0.9;
}

.error-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.error-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Responsividade */
@media (max-width: 1400px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .radar-screen {
        width: 500px;
        height: 500px;
    }
    
    .info-panel {
        order: -1;
        max-width: 500px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .radar-screen {
        width: 350px;
        height: 350px;
    }
    
    .radar-legend {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
}

/* Seções de informações */
.info-section {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
    border: 1px solid rgba(0, 255, 65, 0.2);
}

.section-title {
    color: #00ff41;
    font-size: 1.1rem;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 255, 65, 0.3);
    font-family: 'Orbitron', monospace;
}

.security-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.security-badge.wpa3 {
    background: linear-gradient(45deg, #00ff41, #00cc33);
    color: #000;
}

.security-badge.wpa2 {
    background: linear-gradient(45deg, #32CD32, #228B22);
    color: #fff;
}

.security-badge.wpa {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #000;
}

.security-badge.wep {
    background: linear-gradient(45deg, #ff6600, #ff4400);
    color: #fff;
}

.security-badge.aberto {
    background: linear-gradient(45deg, #ff4444, #cc0000);
    color: #fff;
}

.band-badge {
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
}

.band-badge.freq-24ghz {
    background: linear-gradient(45deg, #4169E1, #6495ED);
    color: #fff;
}

.band-badge.freq-5ghz {
    background: linear-gradient(45deg, #9370DB, #8A2BE2);
    color: #fff;
}

/* Terminal interativo */
.terminal-container {
    background: #000;
    border: 2px solid #00ff41;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    margin-top: 10px;
    box-shadow: 0 4px 15px rgba(0, 255, 65, 0.3);
}

.terminal-header {
    background: linear-gradient(45deg, #00ff41, #00cc33);
    color: #000;
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.terminal-title {
    font-family: 'Orbitron', monospace;
}

.terminal-clear {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 0, 0, 0.3);
    color: #000;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.terminal-clear:hover {
    background: rgba(0, 0, 0, 0.4);
}

.terminal-output {
    height: 200px;
    overflow-y: auto;
    padding: 10px;
    background: #000;
    color: #00ff41;
    font-size: 0.85rem;
    line-height: 1.4;
}

.terminal-line {
    margin-bottom: 2px;
    word-wrap: break-word;
}

.terminal-command {
    color: #ffffff;
    font-weight: 600;
}

.terminal-info {
    color: #00ccff;
}

.terminal-success {
    color: #00ff41;
}

.terminal-error {
    color: #ff4444;
}

.terminal-output {
    color: #cccccc;
}

.terminal-input-container {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #111;
    border-top: 1px solid #333;
}

.terminal-prompt {
    color: #00ff41;
    font-weight: 600;
    margin-right: 8px;
    white-space: nowrap;
}

.terminal-input {
    flex: 1;
    background: transparent;
    border: none;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    outline: none;
    padding: 5px;
}

.terminal-input::placeholder {
    color: #666;
    font-style: italic;
}

.terminal-output::-webkit-scrollbar {
    width: 6px;
}

.terminal-output::-webkit-scrollbar-track {
    background: #222;
}

.terminal-output::-webkit-scrollbar-thumb {
    background: #00ff41;
    border-radius: 3px;
}

.terminal-output::-webkit-scrollbar-thumb:hover {
    background: #00cc33;
}

/* Animações para o terminal */
@keyframes terminalBlink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.terminal-cursor {
    animation: terminalBlink 1s infinite;
}

/* Melhorias no modal */
.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.modal-content {
    max-width: 800px;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #00ff41;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #00cc33;
}
