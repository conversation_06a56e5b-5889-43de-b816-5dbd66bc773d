<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WiFi Radar Desktop</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">
                <span class="radar-icon">📡</span>
                WiFi Radar Desktop
            </h1>
            <div class="controls">
                <button id="scanBtn" class="scan-btn">
                    <span class="btn-icon">🔄</span>
                    Escanear Agora
                </button>
                <button id="autoScanBtn" class="auto-scan-btn active">⏱️ Auto-Scan: ON</button>
                <div class="status" id="status">
                    <span class="status-dot"></span>
                    Iniciando...
                </div>
                <div class="app-info">
                    <span class="desktop-badge">🖥️ Desktop App</span>
                    <span class="real-data-badge">📊 Dados Reais</span>
                </div>
            </div>
        </header>

        <main class="main-content">
            <div class="radar-container">
                <div class="radar-screen" id="radarScreen">
                    <!-- Círculos concêntricos do radar -->
                    <div class="radar-circle" style="width: 100px; height: 100px;"></div>
                    <div class="radar-circle" style="width: 200px; height: 200px;"></div>
                    <div class="radar-circle" style="width: 300px; height: 300px;"></div>
                    <div class="radar-circle" style="width: 400px; height: 400px;"></div>
                    <div class="radar-circle" style="width: 500px; height: 500px;"></div>
                    
                    <!-- Linhas do radar -->
                    <div class="radar-line radar-line-h"></div>
                    <div class="radar-line radar-line-v"></div>
                    <div class="radar-line radar-line-d1"></div>
                    <div class="radar-line radar-line-d2"></div>
                    
                    <!-- Centro do radar (usuário) -->
                    <div class="radar-center">
                        <div class="user-dot"></div>
                        <span class="user-label">Seu PC</span>
                    </div>
                    
                    <!-- Sweep do radar -->
                    <div class="radar-sweep" id="radarSweep"></div>
                    
                    <!-- Pontos dos roteadores serão inseridos aqui -->
                </div>
                
                <div class="radar-legend">
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #00ff41;"></div>
                        <span>Sinal Forte (&gt; -50 dBm)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #ffff00;"></div>
                        <span>Sinal Médio (-50 a -70 dBm)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #ff6600;"></div>
                        <span>Sinal Fraco (-70 a -85 dBm)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #ff0000;"></div>
                        <span>Sinal Muito Fraco (&lt; -85 dBm)</span>
                    </div>
                </div>

                <div class="radar-info">
                    <div class="info-item">
                        <span class="info-label">Última Atualização:</span>
                        <span class="info-value" id="lastUpdate">Aguardando...</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Auto-Scan:</span>
                        <span class="info-value auto-scan-active">🟢 Ativo (5s)</span>
                    </div>
                </div>
            </div>

            <div class="info-panel">
                <div class="panel-header">
                    <h3>Redes WiFi Detectadas</h3>
                    <div class="network-count" id="networkCount">0 redes</div>
                </div>
                <div class="networks-list" id="networksList">
                    <div class="no-networks">
                        <div class="loading-spinner"></div>
                        <p>Escaneando redes WiFi...</p>
                        <small>Aguarde enquanto detectamos as redes disponíveis</small>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal para detalhes da rede -->
    <div class="modal" id="networkModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Detalhes da Rede WiFi</h2>
                <button class="modal-close" id="modalClose">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Conteúdo será inserido dinamicamente -->
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="modalCloseBtn">Fechar</button>
            </div>
        </div>
    </div>

    <!-- Notificação de erro -->
    <div class="error-notification" id="errorNotification">
        <div class="error-content">
            <span class="error-icon">⚠️</span>
            <div class="error-text">
                <div class="error-title">Erro no Escaneamento</div>
                <div class="error-message" id="errorMessage"></div>
            </div>
            <button class="error-close" id="errorClose">&times;</button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
